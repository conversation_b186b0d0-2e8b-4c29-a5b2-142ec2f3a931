#!/bin/bash

# Rent Report Email Batch Scheduler Script for Cron Job
# This script runs the rent report email batch processing in CLI mode

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Set working directory to project root
cd "$PROJECT_DIR" || {
    echo "Error: Cannot change to project directory: $PROJECT_DIR"
    exit 1
}

# Set environment variables
export RMBASE_FILE_CFG="local.ini"

# Set log file path (using src/logs for consistency)
LOG_FILE="$PROJECT_DIR/src/logs/rent_report_batch_scheduler.log"
LOG_DIR="$(dirname "$LOG_FILE")"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to check if the CLI binary exists
check_binary() {
    if [ ! -f "./cli_rent_report_batch" ]; then
        log_message "ERROR: CLI binary './cli_rent_report_batch' not found"
        log_message "Please build the CLI first with: cd src && go build -o ../cli_rent_report_batch ../cli/cli_rent_report_batch.go"
        exit 1
    fi
}

# Function to run the rent report batch scheduler
run_scheduler() {
    log_message "Starting rent report email batch processing..."

    # Run the scheduler from src directory so logs go to src/logs
    cd "$PROJECT_DIR/src"
    output=$(../cli_rent_report_batch 2>&1)
    local exit_code=$?
    cd "$PROJECT_DIR"

    if [ $exit_code -eq 0 ]; then
        log_message "Rent report email batch processing completed successfully"
        log_message "CLI output: $output"
    else
        log_message "ERROR: Rent report email batch processing failed with exit code: $exit_code"
        log_message "CLI error output: $output"
        exit $exit_code
    fi
}

# Main execution
main() {
    log_message "=== Rent Report Email Batch Scheduler Cron Job Started ==="
    
    # Check if binary exists
    check_binary
    
    # Run the scheduler
    run_scheduler
    
    log_message "=== Rent Report Email Batch Scheduler Cron Job Completed ==="
}

# Execute main function
main "$@"
