#!/bin/bash

# Auto Payment Scheduler Script for Cron Job
# This script runs the auto payment scheduler in CLI mode

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Set working directory to project root
cd "$PROJECT_DIR" || {
    echo "Error: Cannot change to project directory: $PROJECT_DIR"
    exit 1
}

# Set environment variables
export RMBASE_FILE_CFG="local.ini"

# Set log file path (using src/logs for consistency)
LOG_FILE="$PROJECT_DIR/src/logs/auto_payment_scheduler.log"
LOG_DIR="$(dirname "$LOG_FILE")"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to check if the application binary exists
check_binary() {
    if [ ! -f "./cli_auto_payment" ]; then
        log_message "ERROR: CLI binary './cli_auto_payment' not found"
        log_message "Please build the CLI first with: cd src && go build -o ../cli_auto_payment ../cli/cli_auto_payment.go"
        exit 1
    fi
}

# Function to run the auto payment scheduler
run_scheduler() {
    log_message "Starting auto payment scheduler..."
    
    # Run the scheduler from src directory so logs go to src/logs
    cd "$PROJECT_DIR/src"
    ../cli_auto_payment
    local exit_code=$?
    cd "$PROJECT_DIR"
    
    if [ $exit_code -eq 0 ]; then
        log_message "Auto payment scheduler completed successfully"
    else
        log_message "ERROR: Auto payment scheduler failed with exit code: $exit_code"
        exit $exit_code
    fi
}

# Main execution
main() {
    log_message "=== Auto Payment Scheduler Cron Job Started ==="
    
    # Check if binary exists
    check_binary
    
    # Run the scheduler
    run_scheduler
    
    log_message "=== Auto Payment Scheduler Cron Job Completed ==="
}

# Execute main function
main "$@"
