# Rent Report Application Configuration (TOML format)
# This file contains all configuration for the application including goupload

# Database configuration
[dbs]
verbose = 3

[dbs.rr]
uri = "mongodb://r1:r1@************:27017/rr?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

[dbs.rr_test]
uri = "mongodb://r1:r1@************:27017/rr_test?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

[dbs.tmp]
uri = "mongodb://r1:r1@************:27017/tmp?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

# Server configuration
[server]
port = "8089"
baseUrl = "http://localhost:8089"
static_path = "web/dist"
serve_static = true
developer_mode = true

# Logging configuration
[golog]
dir = "logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

# OAuth client configuration
[oauthClient]
clientId = "report_rentals"
clientSecret = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib29lzajna"
redirectUrl = "https://d8w.realmaster.com/v1/auth/oauth/callback"
authUrl = "https://d8w.realmaster.com/provider/authorize"
tokenUrl = "https://d8w.realmaster.com/provider/token"
userInfoUrl = "https://d8w.realmaster.com/provider/userinfo"

# Encryption configuration
[encryption]
key = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib2"

# Authentication configuration
[auth]
jwtSecret = "rUeRzaSyCzCBgaRfib2b9lzajneH2MiT"

# Stripe configuration
[stripe]
secret_key = "sk_test_51RYw3ERdRW2qyPyrKO4b5ZdEr5hyioKB7XIhytmyfoYx1yDx8I9b42sSemwBPmiL3pCfEcYXo2NvhECvTGPcpVCR00pCRBwLIF"
webhook_secret = "whsec_3ab1b9d27a7fc917f2b599da1a518d34b563c7351bd828b641a71156375dd116"

# Auto payment configuration
[autoPayment]
enabled = true
executionHour = 1
maxRetries = 3

# Pre-defined collections for gomail logging
[preDefColls.mailLog]
collName = "mail_log"
dbName = "rr"

[preDefColls.mailLog.options]
expireAfterSeconds = 2592000

# Mail engine configuration
[mailEngine.smtp]
service = "gmail"

[mailEngine.gmail]
service = "gmail"
from = "Real Master Info <<EMAIL>>"
defaultEmail = "<EMAIL>"

[mailEngine.gmail.auth]
user = "<EMAIL>"
pass = "3GQi18O5H80zbjh3"
host = "cac1.realmaster.me"
port = 587

[mailEngine.ses]
region = "us-east-1"
accessKeyId = "placeholder"
secretAccessKey = "placeholder"

[mailEngine.sesH]
region = "us-east-1"
accessKeyId = "placeholder"
secretAccessKey = "placeholder"

[mailEngine.rmMail]
defaultEmail = "<EMAIL>"
url = "https://ml1.realmaster.cc/send"

[mailEngine.mockMail]
mock = false
verbose = 1

# 租金报告邮件批处理配置
[rentReportEmailBatch]
enabled = true
processIntervalMinutes = 3  # 每3分钟处理一次队列（测试用）
processWindowMinutes = 2    # 处理前2分钟的记录（测试用）
cleanupDays = 7            # 清理7天前的记录

# Metro2上报通知邮件配置
[metro2NotificationEmail]
enabled = true
delayHours = 0             # Metro2生成后立即发送（测试用）
delayMinutes = 1           # Metro2生成后1分钟发送通知邮件（测试用）
processIntervalMinutes = 1  # 每1分钟检查一次延迟任务（测试用）
cleanupDays = 30           # 清理30天前的任务记录

# 欠款提醒邮件配置
[debtReminderEmail]
enabled = true
sendDay = 15               # 每月15号发送（生产环境）
testMode = true            # 测试模式：启动后立即发送
processIntervalMinutes = 60 # 每60分钟检查一次（生产环境）

# Metro2自动生成和邮件发送配置
[metro2AutoGeneration]
enabled = true
sendDay = 21                    # 每月21号执行
sendHour = 9                    # 上午9点执行
recipientEmail = "<EMAIL>"  # 接收邮箱
processIntervalMinutes = 60     # 检查间隔（分钟）
testMode = true                 # 测试模式：启动后立即发送

# Global write options for goupload
[write_options]
max_retries = 3
retry_delay = "1s"
s3_timeout = "30s"
chunk_size = 5242880  # 5MB
enable_logging = true
validate_content = true
enable_metadata = true

# User upload configuration
[userupload]
site = "TEST"

  # Lease documents upload configuration
  [[userupload.types]]
    entryName = "lease_documents"
    prefix = "/lease_docs"
    tmpPath = "/tmp/goupload/lease_docs"
    maxSize = "50MB"  # Increased from 10MB to 50MB
    storage = [
      { type = "local", path = "uploads/lease_documents" }
    ]

  # Property documents upload configuration
  [[userupload.types]]
    entryName = "property_documents"
    prefix = "/property_docs"
    tmpPath = "/tmp/goupload/property_docs"
    maxSize = "50MB"  # Increased from 10MB to 50MB
    storage = [
      { type = "local", path = "uploads/property_documents" }
    ]

  # Problem report files upload configuration
  [[userupload.types]]
    entryName = "problem_reports"
    prefix = "/problem_files"
    tmpPath = "/tmp/goupload/problem_files"
    maxSize = "50MB"  # Increased from 10MB to 50MB
    storage = [
      { type = "local", path = "uploads/problem_reports" }
    ]

  # Metro2 report files upload configuration
  [[userupload.types]]
    entryName = "metro2_reports"
    prefix = "/metro2_files"
    tmpPath = "/tmp/goupload/metro2_files"
    maxSize = "50MB"  # Metro2 files and JSON backup files
    storage = [
      { type = "local", path = "uploads/metro2_reports" }
    ]

# Connection sources configuration (empty for local-only setup)
[connection_sources]

# L2 directory configuration
#[source_l2_size]
#TEST = 256  # Number of L2 directories for rent report site
