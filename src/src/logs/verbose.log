2025-08-05T15:07:01.412-04:00 level=DEBUG msg=Command started, serverConnectionId=18645, databaseName="admin", driverConnectionId=1, operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "CLxaCyl0RLOphGQSZgvKgQ==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, commandName="ping", message="Command started", requestId=1, serverHost="************"
2025-08-05T15:07:01.412-04:00 level=DEBUG msg=Command succeeded, databaseName="admin", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=1, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420813
    }
  }
}, commandName="ping", serverPort=27017, serverConnectionId=18645
2025-08-05T15:07:01.415-04:00 level=DEBUG msg=Command started, requestId=13, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "1D8r5rlGTVK5kAJvVXMcMw==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, serverConnectionId=18650, commandName="ping", message="Command started", operationId=0, serverHost="************", databaseName="admin", driverConnectionId=1
2025-08-05T15:07:01.415-04:00 level=DEBUG msg=Command succeeded, message="Command succeeded", operationId=0, requestId=13, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420813
    }
  }
}, serverPort=27017, commandName="ping", serverHost="************", serverConnectionId=18650, databaseName="admin", driverConnectionId=1
2025-08-05T15:07:01.419-04:00 level=DEBUG msg=Command started, message="Command started", serverHost="************", serverPort=27017, commandName="ping", databaseName="admin", driverConnectionId=1, operationId=0, requestId=25, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "7ihL+BFIQH+ZN7Cj4DVF2w==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverConnectionId=18654
2025-08-05T15:07:01.419-04:00 level=DEBUG msg=Command succeeded, databaseName="admin", message="Command succeeded", operationId=0, requestId=25, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420813
    }
  }
}, serverPort=27017, serverConnectionId=18654, commandName="ping", driverConnectionId=1, durationMS=0
2025-08-05T15:07:01.422-04:00 level=DEBUG msg=Command started, commandName="find", databaseName="rr", operationId=0, requestId=37, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "$readPreference": {
    "mode": "nearest"
  },
  "filter": {
    "completed": false,
    "scheduledTime": {
      "$lte": {
        "$date": {
          "$numberLong": "1754420821422"
        }
      }
    }
  },
  "find": "metro2_notification_tasks",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "7ihL+BFIQH+ZN7Cj4DVF2w==",
        "subType": "04"
      }
    }
  }
}, serverPort=27017, driverConnectionId=1, message="Command started", serverConnectionId=18654
2025-08-05T15:07:01.422-04:00 level=DEBUG msg=Command succeeded, commandName="find", databaseName="rr", message="Command succeeded", operationId=0, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [],
    "id": {
      "$numberLong": "0"
    },
    "ns": "rr.metro2_notification_tasks"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420813
    }
  }
}, driverConnectionId=1, requestId=37, durationMS=0, serverPort=27017, serverConnectionId=18654
2025-08-05T15:07:01.423-04:00 level=DEBUG "MongoDB Find operation (0.00s) on metro2_notification_tasks: [map[completed:false scheduledTime:map[$lte:2025-08-05 15:07:01.422242 -0400 EDT m=+0.026921959]]]"
2025-08-05T15:07:01.424-04:00 level=DEBUG msg=Command started, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "delete": "metro2_notification_tasks",
  "deletes": [
    {
      "limit": {
        "$numberInt": "0"
      },
      "q": {
        "completed": true,
        "createdAt": {
          "$lt": {
            "$date": {
              "$numberLong": "1751828821424"
            }
          }
        }
      }
    }
  ],
  "lsid": {
    "id": {
      "$binary": {
        "base64": "7ihL+BFIQH+ZN7Cj4DVF2w==",
        "subType": "04"
      }
    }
  },
  "ordered": true
}, serverConnectionId=18654, commandName="delete", databaseName="rr", driverConnectionId=1, message="Command started", operationId=0, requestId=38, serverHost="************", serverPort=27017
2025-08-05T15:07:01.425-04:00 level=DEBUG msg=Command succeeded, message="Command succeeded", operationId=0, serverHost="************", serverPort=27017, commandName="delete", databaseName="rr", requestId=38, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "electionId": {
    "$oid": "7fffffff0000000000000009"
  },
  "n": {
    "$numberInt": "0"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "opTime": {
    "t": {
      "$numberLong": "9"
    },
    "ts": {
      "$timestamp": {
        "i": 1,
        "t": 1754420813
      }
    }
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420813
    }
  }
}, serverConnectionId=18654, driverConnectionId=1
2025-08-05T15:07:01.425-04:00 level=DEBUG "MongoDB DeleteMany operation (0.00s) on metro2_notification_tasks: [map[completed:true createdAt:map[$lt:2025-07-06 15:07:01.424443 -0400 EDT]]]"
2025-08-05T15:07:40.123-04:00 level=DEBUG msg=Command started, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420853
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "LDRjpM23SaeZNrLbCYJbLQ==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverConnectionId=18669, commandName="ping", message="Command started", operationId=0, serverPort=27017, databaseName="admin", driverConnectionId=1, requestId=1, serverHost="************"
2025-08-05T15:07:40.125-04:00 level=DEBUG msg=Command succeeded, operationId=0, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420853
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420853
    }
  }
}, serverPort=27017, serverConnectionId=18669, driverConnectionId=1, message="Command succeeded", requestId=1, durationMS=0, commandName="ping", databaseName="admin"
2025-08-05T15:07:40.128-04:00 level=DEBUG msg=Command started, message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420853
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "/8G1msN6QHCUz5/EPeL5Aw==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, driverConnectionId=1, operationId=0, requestId=13, serverPort=27017, serverConnectionId=18673, commandName="ping", databaseName="admin"
2025-08-05T15:07:40.13-04:00 level=DEBUG msg=Command succeeded, driverConnectionId=1, message="Command succeeded", requestId=13, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420853
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420853
    }
  }
}, serverPort=27017, databaseName="admin", operationId=0, serverConnectionId=18673, commandName="ping"
2025-08-05T15:07:40.137-04:00 level=DEBUG msg=Command started, commandName="ping", databaseName="admin", driverConnectionId=1, message="Command started", requestId=25, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420853
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "NpVRrnirSDST5dK0VlXEWw==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverConnectionId=18678, operationId=0, serverHost="************", serverPort=27017
2025-08-05T15:07:40.138-04:00 level=DEBUG msg=Command succeeded, requestId=25, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420853
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420853
    }
  }
}, serverPort=27017, serverConnectionId=18678, commandName="ping", databaseName="admin", driverConnectionId=1, message="Command succeeded", operationId=0
2025-08-05T15:07:40.143-04:00 level=DEBUG msg=Command started, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420853
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "$readPreference": {
    "mode": "nearest"
  },
  "filter": {
    "$or": [
      {
        "endDt": ""
      },
      {
        "endDt": {
          "$gte": "2025-08-05"
        }
      }
    ],
    "startDt": {
      "$lte": "2025-08-05"
    },
    "status": "active"
  },
  "find": "leases",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "/8G1msN6QHCUz5/EPeL5Aw==",
        "subType": "04"
      }
    }
  }
}, serverConnectionId=18673, message="Command started", operationId=0, requestId=37, serverPort=27017, commandName="find", databaseName="rr", driverConnectionId=1, serverHost="************"
2025-08-05T15:07:40.144-04:00 level=DEBUG msg=Command succeeded, serverConnectionId=18673, commandName="find", databaseName="rr", durationMS=0, driverConnectionId=1, message="Command succeeded", operationId=0, requestId=37, serverHost="************", reply="{\"cursor\": {\"firstBatch\": [{\"_id\": \"zdCcJx00zzc\",\"otherDep\": {\"$numberDouble\":\"0.0\"},\"rentRep\": true,\"keyDep\": {\"$numberDouble\":\"0.0\"},\"rentDueDay\": {\"$numberInt\":\"1\"},\"owingBal\": {\"$numberDouble\":\"0.0\"},\"lastPmtDt\": \"2025-07-01\",\"invId\": \"\",\"ctnts\": [{\"phone\": \"EJSjYamksJpflxXGgJE2AUyzPXgVV5txb8rZmKZs1w/J7ncioN0=\",\"_id\": \"gYhEM4rmuDP\",\"midNm\": \"Rose\",\"lastNm\": \"Thompson\",\"isProtected\": true,\"leaseId\": \"zdCcJx00zzc\",\"usrId\": \"\",\"tenantId\": \"\",\"email\": \"<EMAIL>\",\"orgId\": \"\",\"firstNm\": \"Emily\",\"notes\": \"\",\"sin\": \"HNgrADvBhSh1xoLbrJNqLN1+NBso64D8cq5TSf+0KvCxe3t1fg==\",\"dob\": \"0NeB0eAc+qdesbU3uwz73puoIwYJhSLWQOt8dtnfnrv13FdBdwi1l0dNn7PUOk7x\"}],\"ptnts\": null,\"fileName\": \"\",\"startDt\": \"2025-05-01\",\"rentDep\": {\"$numberDouble\":\"0.0\"},\"fileSize\": {\"$numberLong\":\"0\"},\"_ts\": {\"$date\":{\"$numberLong\":\"1752088351247\"}},\"tenantId\": \"gYhEM4rmuDP\",\"endDt\": \"\",\"addFees\": {\"$numberDouble\":\"0.0\"},\"autoPay\": true,\"fileId\": \"\",\"fileType\": \"\",\"_mt\": {\"$date\":{\"$numberLong\":\"1754415966853\"}},\"propId\": \"xSWg6t2p...", serverPort=27017
2025-08-05T15:07:40.145-04:00 level=DEBUG "MongoDB FindToArray operation (0.00s) on leases: [map[$or:[map[endDt:] map[endDt:map[$gte:2025-08-05]]] startDt:map[$lte:2025-08-05] status:active]]"
2025-08-05T15:07:40.146-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="gYhEM4rmuDP"
2025-08-05T15:07:40.148-04:00 level=DEBUG msg=Decrypting SIN for tenant, tenantId="gYhEM4rmuDP"
2025-08-05T15:07:40.15-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="7hgpM87VjpZ"
2025-08-05T15:07:40.15-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="7hgpM87VjpZ"
2025-08-05T15:07:40.152-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="B258pS4ZOlS"
2025-08-05T15:07:40.152-04:00 level=DEBUG msg=Decrypting SIN for tenant, tenantId="B258pS4ZOlS"
2025-08-05T15:07:40.154-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="7W6o9bCORoe"
2025-08-05T15:07:40.155-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="7W6o9bCORoe"
2025-08-05T15:07:40.156-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="zBONSHzGrq2"
2025-08-05T15:07:40.157-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="zBONSHzGrq2"
2025-08-05T15:07:40.158-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="Tab1RcaTQcL"
2025-08-05T15:07:40.159-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="Tab1RcaTQcL"
2025-08-05T15:07:40.16-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="TrFCn0aUOf2"
2025-08-05T15:07:40.16-04:00 level=DEBUG msg=Decrypting SIN for tenant, tenantId="TrFCn0aUOf2"
2025-08-05T15:07:40.161-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="dAzboElHPZ5"
2025-08-05T15:07:40.162-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="dAzboElHPZ5"
2025-08-05T15:07:40.163-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="TOFR3pmKRAi"
2025-08-05T15:07:40.164-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="TOFR3pmKRAi"
2025-08-05T15:07:40.165-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="SVWNb76w1YE"
2025-08-05T15:07:40.165-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="SVWNb76w1YE"
2025-08-05T15:07:40.166-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="W5oPsp6A7Gu"
2025-08-05T15:07:40.166-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="W5oPsp6A7Gu"
2025-08-05T15:07:52.627-04:00 level=DEBUG msg=Command started, commandName="ping", databaseName="admin", message="Command started", operationId=0, requestId=1, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "q2jWv4qCSNypIXoYAen8aw==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, driverConnectionId=1, serverPort=27017, serverConnectionId=18682
2025-08-05T15:07:52.627-04:00 level=DEBUG msg=Command succeeded, databaseName="admin", message="Command succeeded", serverHost="************", durationMS=0, serverPort=27017, serverConnectionId=18682, commandName="ping", driverConnectionId=1, operationId=0, requestId=1, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420863
    }
  }
}
2025-08-05T15:07:52.63-04:00 level=DEBUG msg=Command started, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "ZiVLXs94SuO7mMaom7qnsw==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, serverConnectionId=18686, commandName="ping", message="Command started", requestId=13, serverHost="************", databaseName="admin", driverConnectionId=1, operationId=0
2025-08-05T15:07:52.63-04:00 level=DEBUG msg=Command succeeded, commandName="ping", databaseName="admin", message="Command succeeded", requestId=13, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420863
    }
  }
}, serverPort=27017, driverConnectionId=1, operationId=0, serverConnectionId=18686
2025-08-05T15:07:52.633-04:00 level=DEBUG msg=Command started, message="Command started", operationId=0, serverHost="************", serverPort=27017, serverConnectionId=18690, commandName="ping", databaseName="admin", requestId=25, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "lL2N7GIbTomp3CFQxyfGFw==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, driverConnectionId=1
2025-08-05T15:07:52.634-04:00 level=DEBUG msg=Command succeeded, databaseName="admin", serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420863
    }
  }
}, serverPort=27017, serverConnectionId=18690, commandName="ping", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=25
2025-08-05T15:07:52.637-04:00 level=DEBUG msg=Command started, serverConnectionId=18682, commandName="find", driverConnectionId=1, message="Command started", operationId=0, requestId=37, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "$readPreference": {
    "mode": "nearest"
  },
  "filter": {
    "completed": false,
    "scheduledTime": {
      "$lte": {
        "$date": {
          "$numberLong": "1754420872637"
        }
      }
    }
  },
  "find": "metro2_notification_tasks",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "q2jWv4qCSNypIXoYAen8aw==",
        "subType": "04"
      }
    }
  }
}, serverPort=27017, databaseName="rr", serverHost="************"
2025-08-05T15:07:52.637-04:00 level=DEBUG msg=Command succeeded, driverConnectionId=1, operationId=0, requestId=37, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [],
    "id": {
      "$numberLong": "0"
    },
    "ns": "rr.metro2_notification_tasks"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420863
    }
  }
}, serverPort=27017, commandName="find", databaseName="rr", message="Command succeeded", durationMS=0, serverConnectionId=18682
2025-08-05T15:07:52.638-04:00 level=DEBUG "MongoDB Find operation (0.00s) on metro2_notification_tasks: [map[completed:false scheduledTime:map[$lte:2025-08-05 15:07:52.637004 -0400 EDT m=+0.024521835]]]"
2025-08-05T15:07:52.639-04:00 level=DEBUG msg=Command started, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "delete": "metro2_notification_tasks",
  "deletes": [
    {
      "limit": {
        "$numberInt": "0"
      },
      "q": {
        "completed": true,
        "createdAt": {
          "$lt": {
            "$date": {
              "$numberLong": "1751828872639"
            }
          }
        }
      }
    }
  ],
  "lsid": {
    "id": {
      "$binary": {
        "base64": "q2jWv4qCSNypIXoYAen8aw==",
        "subType": "04"
      }
    }
  },
  "ordered": true
}, serverPort=27017, commandName="delete", driverConnectionId=1, operationId=0, serverConnectionId=18682, databaseName="rr", message="Command started", requestId=38
2025-08-05T15:07:52.64-04:00 level=DEBUG msg=Command succeeded, databaseName="rr", driverConnectionId=1, message="Command succeeded", serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "electionId": {
    "$oid": "7fffffff0000000000000009"
  },
  "n": {
    "$numberInt": "0"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "opTime": {
    "t": {
      "$numberLong": "9"
    },
    "ts": {
      "$timestamp": {
        "i": 1,
        "t": 1754420863
      }
    }
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754420863
    }
  }
}, serverPort=27017, commandName="delete", operationId=0, requestId=38, durationMS=0, serverConnectionId=18682
2025-08-05T15:07:52.64-04:00 level=DEBUG "MongoDB DeleteMany operation (0.00s) on metro2_notification_tasks: [map[completed:true createdAt:map[$lt:2025-07-06 15:07:52.639561 -0400 EDT]]]"
2025-08-05T15:15:32.363-04:00 level=DEBUG msg=Command started, databaseName="admin", driverConnectionId=1, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421323
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "K6XE1ko7QqGh+AVA0K08IA==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, serverConnectionId=18706, commandName="ping", message="Command started", operationId=0, requestId=1, serverHost="************"
2025-08-05T15:15:32.364-04:00 level=DEBUG msg=Command succeeded, databaseName="admin", driverConnectionId=1, message="Command succeeded", requestId=1, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421323
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421323
    }
  }
}, operationId=0, serverHost="************", serverPort=27017, serverConnectionId=18706, commandName="ping"
2025-08-05T15:15:32.368-04:00 level=DEBUG msg=Command started, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421323
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "kW+xWI2BSd2gMjhO5UiCHA==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, commandName="ping", requestId=13, serverConnectionId=18709, databaseName="admin", driverConnectionId=1, message="Command started", operationId=0
2025-08-05T15:15:32.369-04:00 level=DEBUG msg=Command succeeded, requestId=13, durationMS=0, serverPort=27017, serverConnectionId=18709, commandName="ping", driverConnectionId=1, message="Command succeeded", operationId=0, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421323
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421323
    }
  }
}, databaseName="admin"
2025-08-05T15:15:32.374-04:00 level=DEBUG msg=Command started, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421323
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "pjmART6BTqOxv09UbDeS9g==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverConnectionId=18714, commandName="ping", databaseName="admin", driverConnectionId=1, message="Command started", operationId=0, serverPort=27017, requestId=25
2025-08-05T15:15:32.376-04:00 level=DEBUG msg=Command succeeded, serverConnectionId=18714, driverConnectionId=1, operationId=0, requestId=25, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421323
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421323
    }
  }
}, commandName="ping", databaseName="admin", message="Command succeeded", serverHost="************", durationMS=0, serverPort=27017
2025-08-05T15:15:32.382-04:00 level=DEBUG msg=Command started, driverConnectionId=1, message="Command started", operationId=0, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421323
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "$readPreference": {
    "mode": "nearest"
  },
  "filter": {
    "$or": [
      {
        "endDt": ""
      },
      {
        "endDt": {
          "$gte": "2025-08-05"
        }
      }
    ],
    "startDt": {
      "$lte": "2025-08-05"
    },
    "status": "active"
  },
  "find": "leases",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "pjmART6BTqOxv09UbDeS9g==",
        "subType": "04"
      }
    }
  }
}, serverConnectionId=18714, commandName="find", requestId=37, serverPort=27017, databaseName="rr"
2025-08-05T15:15:32.383-04:00 level=DEBUG msg=Command succeeded, driverConnectionId=1, operationId=0, requestId=37, commandName="find", message="Command succeeded", serverHost="************", durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": \"zdCcJx00zzc\",\"otherDep\": {\"$numberDouble\":\"0.0\"},\"rentRep\": true,\"keyDep\": {\"$numberDouble\":\"0.0\"},\"rentDueDay\": {\"$numberInt\":\"1\"},\"owingBal\": {\"$numberDouble\":\"0.0\"},\"lastPmtDt\": \"2025-07-01\",\"invId\": \"\",\"ctnts\": [{\"phone\": \"EJSjYamksJpflxXGgJE2AUyzPXgVV5txb8rZmKZs1w/J7ncioN0=\",\"_id\": \"gYhEM4rmuDP\",\"midNm\": \"Rose\",\"lastNm\": \"Thompson\",\"isProtected\": true,\"leaseId\": \"zdCcJx00zzc\",\"usrId\": \"\",\"tenantId\": \"\",\"email\": \"<EMAIL>\",\"orgId\": \"\",\"firstNm\": \"Emily\",\"notes\": \"\",\"sin\": \"HNgrADvBhSh1xoLbrJNqLN1+NBso64D8cq5TSf+0KvCxe3t1fg==\",\"dob\": \"0NeB0eAc+qdesbU3uwz73puoIwYJhSLWQOt8dtnfnrv13FdBdwi1l0dNn7PUOk7x\"}],\"ptnts\": null,\"fileName\": \"\",\"startDt\": \"2025-05-01\",\"rentDep\": {\"$numberDouble\":\"0.0\"},\"fileSize\": {\"$numberLong\":\"0\"},\"_ts\": {\"$date\":{\"$numberLong\":\"1752088351247\"}},\"tenantId\": \"gYhEM4rmuDP\",\"endDt\": \"\",\"addFees\": {\"$numberDouble\":\"0.0\"},\"autoPay\": true,\"fileId\": \"\",\"fileType\": \"\",\"_mt\": {\"$date\":{\"$numberLong\":\"1754415966853\"}},\"propId\": \"xSWg6t2p...", serverPort=27017, serverConnectionId=18714, databaseName="rr"
2025-08-05T15:15:32.384-04:00 level=DEBUG "MongoDB FindToArray operation (0.00s) on leases: [map[$or:[map[endDt:] map[endDt:map[$gte:2025-08-05]]] startDt:map[$lte:2025-08-05] status:active]]"
2025-08-05T15:15:32.385-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="gYhEM4rmuDP"
2025-08-05T15:15:32.386-04:00 level=DEBUG msg=Decrypting SIN for tenant, tenantId="gYhEM4rmuDP"
2025-08-05T15:15:32.389-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="7hgpM87VjpZ"
2025-08-05T15:15:32.389-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="7hgpM87VjpZ"
2025-08-05T15:15:32.391-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="B258pS4ZOlS"
2025-08-05T15:15:32.391-04:00 level=DEBUG msg=Decrypting SIN for tenant, tenantId="B258pS4ZOlS"
2025-08-05T15:15:32.392-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="7W6o9bCORoe"
2025-08-05T15:15:32.393-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="7W6o9bCORoe"
2025-08-05T15:15:32.394-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="zBONSHzGrq2"
2025-08-05T15:15:32.395-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="zBONSHzGrq2"
2025-08-05T15:15:32.396-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="Tab1RcaTQcL"
2025-08-05T15:15:32.396-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="Tab1RcaTQcL"
2025-08-05T15:15:32.397-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="TrFCn0aUOf2"
2025-08-05T15:15:32.398-04:00 level=DEBUG msg=Decrypting SIN for tenant, tenantId="TrFCn0aUOf2"
2025-08-05T15:15:32.399-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="dAzboElHPZ5"
2025-08-05T15:15:32.399-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="dAzboElHPZ5"
2025-08-05T15:15:32.401-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="TOFR3pmKRAi"
2025-08-05T15:15:32.402-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="TOFR3pmKRAi"
2025-08-05T15:15:32.404-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="SVWNb76w1YE"
2025-08-05T15:15:32.404-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="SVWNb76w1YE"
2025-08-05T15:15:32.405-04:00 level=DEBUG msg=UnmarshalBSON called for tenant, tenantId="W5oPsp6A7Gu"
2025-08-05T15:15:32.405-04:00 level=DEBUG msg=Decrypting DOB for tenant, tenantId="W5oPsp6A7Gu"
2025-08-05T15:15:39.827-04:00 level=DEBUG msg=Command started, databaseName="admin", operationId=0, serverPort=27017, serverConnectionId=18717, commandName="ping", driverConnectionId=1, message="Command started", requestId=1, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "g8yDP1yTTaa5z+22ugQuNw==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}
2025-08-05T15:15:39.827-04:00 level=DEBUG msg=Command succeeded, commandName="ping", durationMS=0, serverPort=27017, serverConnectionId=18717, databaseName="admin", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=1, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421333
    }
  }
}
2025-08-05T15:15:39.831-04:00 level=DEBUG msg=Command started, serverPort=27017, serverConnectionId=18721, commandName="ping", driverConnectionId=1, message="Command started", operationId=0, requestId=13, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "LhPoXL07RBi+ModIzgS9zg==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, databaseName="admin", serverHost="************"
2025-08-05T15:15:39.832-04:00 level=DEBUG msg=Command succeeded, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421333
    }
  }
}, serverPort=27017, databaseName="admin", driverConnectionId=1, message="Command succeeded", serverConnectionId=18721, commandName="ping", operationId=0, requestId=13, serverHost="************"
2025-08-05T15:15:39.836-04:00 level=DEBUG msg=Command started, serverConnectionId=18726, commandName="ping", databaseName="admin", driverConnectionId=1, operationId=0, requestId=25, serverHost="************", serverPort=27017, message="Command started", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "$readPreference": {
    "mode": "nearest"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "u19A3WPOTD+J/6Ut9/G48Q==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}
2025-08-05T15:15:39.837-04:00 level=DEBUG msg=Command succeeded, serverPort=27017, commandName="ping", message="Command succeeded", operationId=0, requestId=25, serverHost="************", durationMS=0, serverConnectionId=18726, databaseName="admin", driverConnectionId=1, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421333
    }
  }
}
2025-08-05T15:15:39.842-04:00 level=DEBUG msg=Command started, commandName="find", databaseName="rr", message="Command started", requestId=37, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "$readPreference": {
    "mode": "nearest"
  },
  "filter": {
    "completed": false,
    "scheduledTime": {
      "$lte": {
        "$date": {
          "$numberLong": "1754421339842"
        }
      }
    }
  },
  "find": "metro2_notification_tasks",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "g8yDP1yTTaa5z+22ugQuNw==",
        "subType": "04"
      }
    }
  }
}, driverConnectionId=1, operationId=0, serverPort=27017, serverConnectionId=18717
2025-08-05T15:15:39.843-04:00 level=DEBUG msg=Command succeeded, driverConnectionId=1, message="Command succeeded", serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [],
    "id": {
      "$numberLong": "0"
    },
    "ns": "rr.metro2_notification_tasks"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421333
    }
  }
}, serverPort=27017, commandName="find", databaseName="rr", operationId=0, requestId=37, durationMS=0, serverConnectionId=18717
2025-08-05T15:15:39.844-04:00 level=DEBUG "MongoDB Find operation (0.00s) on metro2_notification_tasks: [map[completed:false scheduledTime:map[$lte:2025-08-05 15:15:39.842477 -0400 EDT m=+0.038778710]]]"
2025-08-05T15:15:39.846-04:00 level=DEBUG msg=Command started, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "rr",
  "delete": "metro2_notification_tasks",
  "deletes": [
    {
      "limit": {
        "$numberInt": "0"
      },
      "q": {
        "completed": true,
        "createdAt": {
          "$lt": {
            "$date": {
              "$numberLong": "1751829339846"
            }
          }
        }
      }
    }
  ],
  "lsid": {
    "id": {
      "$binary": {
        "base64": "g8yDP1yTTaa5z+22ugQuNw==",
        "subType": "04"
      }
    }
  },
  "ordered": true
}, serverConnectionId=18717, commandName="delete", operationId=0, requestId=38, serverPort=27017, databaseName="rr", driverConnectionId=1, message="Command started", serverHost="************"
2025-08-05T15:15:39.847-04:00 level=DEBUG msg=Command succeeded, databaseName="rr", message="Command succeeded", operationId=0, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "electionId": {
    "$oid": "7fffffff0000000000000009"
  },
  "n": {
    "$numberInt": "0"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "opTime": {
    "t": {
      "$numberLong": "9"
    },
    "ts": {
      "$timestamp": {
        "i": 1,
        "t": 1754421333
      }
    }
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754421333
    }
  }
}, serverPort=27017, commandName="delete", driverConnectionId=1, requestId=38, serverConnectionId=18717
2025-08-05T15:15:39.848-04:00 level=DEBUG "MongoDB DeleteMany operation (0.00s) on metro2_notification_tasks: [map[completed:true createdAt:map[$lt:2025-07-06 15:15:39.846374 -0400 EDT]]]"
