2025-08-05T15:07:01.42-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-05T15:07:40.141-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-05T15:07:40.149-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.151-04:00 level=ERROR msg=Failed to unmarshal lease data, file="cli_auto_payment.go:157", error={}
2025-08-05T15:07:40.153-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.156-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.158-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.159-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.161-04:00 level=ERROR msg=Failed to unmarshal lease data, file="cli_auto_payment.go:157", error={}
2025-08-05T15:07:40.162-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.164-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.166-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:40.167-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:07:52.635-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-05T15:15:32.379-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-05T15:15:32.388-04:00 level=ERROR msg=Failed to unmarshal lease data, file="cli_auto_payment.go:157", error={}
2025-08-05T15:15:32.39-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.392-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.394-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.395-04:00 level=ERROR msg=Failed to unmarshal lease data, file="cli_auto_payment.go:157", error={}
2025-08-05T15:15:32.397-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.399-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.4-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.403-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.404-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:32.406-04:00 level=ERROR msg=Failed to unmarshal lease data, error={}, file="cli_auto_payment.go:157"
2025-08-05T15:15:39.84-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
