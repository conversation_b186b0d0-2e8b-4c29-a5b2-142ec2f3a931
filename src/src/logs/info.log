2025-08-05T15:07:01.413-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-05T15:07:01.416-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-05T15:07:01.42-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-05T15:07:01.42-04:00 level=INFO msg=Registered predefined collection, collName="mail_log", collection="mailLog", database="rr"
2025-08-05T15:07:01.42-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-05T15:07:01.421-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-05T15:07:01.421-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-05T15:07:01.423-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-05T15:07:01.424-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-05T15:07:01.426-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-05T15:07:01.426-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-05T15:07:40.125-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-05T15:07:40.131-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-05T15:07:40.139-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-05T15:07:40.14-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-05T15:07:40.141-04:00 level=INFO, value="Starting auto payment scheduler CLI execution..."
2025-08-05T15:07:40.142-04:00 level=INFO, value="Processing auto payments..."
2025-08-05T15:07:40.167-04:00 level=INFO, value="Found 1 active leases for processing (status=active, within valid date range)"
2025-08-05T15:07:40.167-04:00 level=INFO msg=Auto payment processing completed, errorCount=0, successCount=0
2025-08-05T15:07:40.168-04:00 level=INFO, value="Auto payment scheduler CLI completed successfully"
2025-08-05T15:07:52.628-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-05T15:07:52.631-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-05T15:07:52.634-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-05T15:07:52.635-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-05T15:07:52.635-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-05T15:07:52.636-04:00 level=INFO msg=Processing Metro2 notification tasks, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30, delayHours=0
2025-08-05T15:07:52.636-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-05T15:07:52.638-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-05T15:07:52.638-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-05T15:07:52.641-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-05T15:07:52.641-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-05T15:15:32.365-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-05T15:15:32.37-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-05T15:15:32.377-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-05T15:15:32.377-04:00 level=INFO msg=Registered predefined collection, collName="mail_log", collection="mailLog", database="rr"
2025-08-05T15:15:32.38-04:00 level=INFO, value="Starting auto payment scheduler CLI execution..."
2025-08-05T15:15:32.38-04:00 level=INFO, value="Processing auto payments..."
2025-08-05T15:15:32.406-04:00 level=INFO, value="Found 1 active leases for processing (status=active, within valid date range)"
2025-08-05T15:15:32.407-04:00 level=INFO msg=Auto payment processing completed, successCount=0, errorCount=0
2025-08-05T15:15:32.407-04:00 level=INFO, value="Auto payment scheduler CLI completed successfully"
2025-08-05T15:15:39.829-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-05T15:15:39.833-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-05T15:15:39.838-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-05T15:15:39.838-04:00 level=INFO msg=Registered predefined collection, collName="mail_log", collection="mailLog", database="rr"
2025-08-05T15:15:39.84-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-05T15:15:39.841-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-05T15:15:39.841-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-05T15:15:39.844-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-05T15:15:39.845-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-05T15:15:39.848-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-05T15:15:39.849-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
