# System Architecture Document

## Overview

This document describes the system architecture for the Rent Report application, a comprehensive property and lease management system built with modern web technologies and microservices patterns.

## Architecture Overview

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (Vite/JS)     │◄──►│   (Go/Gin)      │◄──►│   (MongoDB)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ Background      │
                    │ Services        │
                    │ (Schedulers)    │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ External        │
                    │ Services        │
                    │ (Stripe/Email)  │
                    └─────────────────┘
```

### Technology Stack

**Backend**:
- **Language**: Go 1.24.4+
- **Framework**: Gin Web Framework
- **Database**: MongoDB 4.4+
- **Authentication**: JWT with refresh tokens
- **File Upload**: goupload library
- **Email**: gomail library
- **Logging**: golog library
- **Configuration**: goconfig (INI format)

**Frontend**:
- **Build Tool**: Vite 5.x
- **Styling**: TailwindCSS 3.x
- **Template Engine**: Mustache/Handlebars
- **HTTP Client**: jQuery/Fetch API
- **Dynamic Loading**: HTMX
- **Package Manager**: npm

**Infrastructure**:
- **Containerization**: Docker with Alpine Linux
- **Reverse Proxy**: Nginx (production)
- **Process Management**: systemd/Docker
- **Build System**: Make-based automation

**Analytics & Monitoring**:
- **Analytics**: Amplitude Analytics with Session Replay
- **User Tracking**: Event tracking and user behavior analysis
- **Performance Monitoring**: Real-time application performance tracking
- **Error Tracking**: Comprehensive error logging and monitoring

**CLI & Automation**:
- **CLI Tools**: 5 command-line tools for batch processing
- **Shell Scripts**: Automated schedulers with cron integration
- **Test Utilities**: Monitoring and testing automation tools
- **Build Automation**: Multi-platform build and deployment system

---

## System Components

### 1. Frontend Architecture

#### Directory Structure
```
src/web/
├── assets/          # Static assets (images, icons)
├── components/      # Reusable UI components
├── pages/          # Application pages
│   ├── home/       # Dashboard pages
│   ├── login/      # Authentication pages
│   ├── property/   # Property management
│   ├── lease/      # Lease management
│   └── account/    # User account pages
├── services/       # API client services
├── styles/         # CSS and styling
├── templates/      # Mustache templates
└── dist/          # Build output
```

#### Build System
- **Development**: Vite dev server with hot reload
- **Production**: Optimized bundle with tree-shaking
- **Asset Processing**: CSS minification, image optimization
- **Proxy Configuration**: API requests proxied to backend

#### Key Features
- **Multi-page Application**: Separate pages for different functions
- **Responsive Design**: Mobile-first approach with TailwindCSS
- **Dynamic Content**: HTMX for seamless content updates
- **Mock API**: Development mock server for frontend testing

### 2. Backend Architecture

#### Directory Structure
```
src/
├── controller/     # HTTP request handlers
├── entities/       # Business logic and data models
├── models/         # Database models
├── middleware/     # HTTP middleware (auth, security, logging)
├── services/       # Background services and schedulers
├── scheduler/      # Automated task scheduling
├── utils/          # Utility functions and helpers
├── config/         # Configuration management
├── auth/           # Authentication and authorization
└── router/         # Route registration system
```

#### Core Components

**Controllers** (18 controllers):
- Authentication, User Management, Properties, Leases
- Tenants, Payments, Invitations, Messages
- Admin, Metro2 Reporting, Referrals, Subscriptions
- System Health, File Downloads, Webhooks

**Middleware Stack**:
- Security Headers (XSS, CSRF, Content Security Policy)
- CORS (Cross-Origin Resource Sharing)
- Request Size Limiting
- Input Validation
- Rate Limiting (configurable)
- Secure Cookie Handling
- Comprehensive Logging

**Background Services**:
- Auto Payment Scheduler (daily rent processing)
- Email Batch Processor (rent report notifications)
- Metro2 Notification Scheduler (credit report notifications)
- Metro2 Auto Generation (monthly credit reports)
- Debt Reminder Scheduler (landlord notifications)

### 3. Database Architecture

#### MongoDB Collections (20+ collections)

**Core Business Data**:
- `users` - User accounts and authentication
- `properties` - Property information and documents
- `leases` - Lease agreements and lifecycle
- `tenants` - Tenant information (encrypted PII)
- `tenant_payments` - Payment records and history

**Supporting Data**:
- `invitations` - Tenant invitation system
- `problems` - Issue reporting and tracking
- `referral_codes` - Referral code management
- `metro2_reports` - Credit reporting data
- `messages` - Communication history

**System Data**:
- `admin_logs` - Administrative action logs
- `rent_report_email_queue` - Email notification queue
- `subplans` - Subscription plans
- `usersubs` - User subscriptions
- `stripeeventlog` - Stripe webhook event logs

#### Data Security
- **Encryption**: AES-256 encryption for PII fields
- **Indexing**: Compound indexes for performance
- **Audit Trail**: Protected records for compliance
- **Resource Scoping**: Multi-tenant data isolation

### 4. Security Architecture

#### Authentication & Authorization
- **JWT Tokens**: Access tokens (15 min) + Refresh tokens (7 days)
- **OAuth2 Integration**: RealMaster provider support
- **Role-Based Access Control**: Admin, normal user, dev, super admin
- **Smart Resource Authentication**: Dynamic permission checking

#### Security Measures
- **Input Validation**: Request sanitization and validation
- **Security Headers**: Comprehensive HTTP security headers
- **CORS Policy**: Configurable cross-origin access control
- **Rate Limiting**: Configurable request rate limiting
- **Secure Cookies**: HttpOnly, Secure, SameSite attributes
- **Request Size Limits**: Protection against large payload attacks

#### Data Protection
- **Encryption at Rest**: PII fields encrypted in database
- **Encryption in Transit**: HTTPS/TLS for all communications
- **Secure File Upload**: File type validation and size limits
- **Audit Logging**: Comprehensive security event logging

---

## Deployment Architecture

### Development Environment
```
Developer Machine
├── Frontend Dev Server (Vite) :3000
├── Backend Server (Go) :8089
├── MongoDB (Local/Remote)
└── File Storage (Local filesystem)
```

### Production Environment
```
Production Server
├── Nginx (Reverse Proxy) :80/443
│   ├── Static Files (/web/*)
│   └── API Proxy (/v1/*)
├── Application Server :8089
│   ├── Go Binary
│   ├── Background Services
│   └── File Upload Handler
├── MongoDB Cluster
└── External Services
    ├── Stripe (Payments)
    └── Email Service
```

### Container Architecture
```dockerfile
# Multi-stage Docker build
FROM node:18-alpine AS frontend-builder
# Frontend build stage

FROM golang:1.24-alpine AS backend-builder  
# Backend build stage

FROM alpine:latest AS runtime
# Production runtime with non-root user
```

#### Container Features
- **Multi-stage Build**: Optimized image size (~50MB final image)
- **Non-root User**: Security best practices (UID 1000)
- **Health Checks**: Container health monitoring via `/health` endpoint
- **Volume Mounts**: Configuration and data persistence
- **Alpine Linux**: Minimal attack surface
- **Resource Limits**: CPU and memory constraints
- **Graceful Shutdown**: SIGTERM handling for clean shutdowns

#### Container Orchestration
```yaml
# Docker Compose example
version: '3.8'
services:
  rent-report:
    image: rent_report:latest
    ports:
      - "8089:8089"
    volumes:
      - ./configs:/app/configs:ro
      - ./uploads:/app/uploads
    environment:
      - GIN_MODE=release
      - MONGODB_URI=mongodb://mongo:27017/rr
    depends_on:
      - mongodb
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8089/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### Build & Deployment Pipeline

#### Make-based Build System
```bash
make setup          # Install dependencies
make start          # Development mode
make full-build     # Complete build
make docker-latest  # Container build
make docker-start   # Container deployment
```

#### Build Features
- **Dependency Management**: Automated Go modules + npm
- **Asset Optimization**: Frontend bundling and compression
- **Cross-platform**: Linux, macOS, Windows support
- **Configuration Management**: Environment-specific configs
- **Documentation**: Automated API documentation generation

---

## Configuration Management

### Configuration Files
- `local.ini` - Development configuration
- `app.ini` - Application settings
- `rain.ini` - Production configuration

### Configuration Categories
- **Server**: Port, base URL, static file serving
- **Database**: MongoDB connection strings
- **Authentication**: JWT secrets, OAuth2 settings
- **Security**: Encryption keys, password policies, user limits
- **Email**: SMTP settings, template configuration
- **File Upload**: Storage paths, size limits (12MB documents, 4MB images)
- **Logging**: Log levels, file paths, security event logging
- **External Services**: Stripe keys, API endpoints
- **Analytics**: Amplitude API key and tracking configuration
- **Schedulers**: Background service timing and retry policies

### Environment Variables
- **Security**: JWT_SECRET, ENCRYPTION_KEY, MIN_PASSWORD_LENGTH
- **Database**: MONGODB_URI, DB_CONNECTION_TIMEOUT_SECONDS
- **External**: STRIPE_API_KEY, STRIPE_WEBHOOK_SECRET
- **Deployment**: GIN_MODE, ENVIRONMENT, RMBASE_FILE_CFG
- **Limits**: FREE_USER_MAX_PROPERTIES, FREE_USER_MAX_LEASES
- **File Upload**: MAX_FILE_SIZE, UPLOAD_PATH, ALLOWED_FILE_TYPES
- **Rate Limiting**: RATE_LIMIT_PER_MINUTE, MAX_REQUEST_SIZE

---

## Monitoring & Observability

### Logging System
- **Structured Logging**: JSON format with contextual fields
- **Log Levels**: Debug, Info, Warn, Error
- **Log Rotation**: Automatic log file management
- **Security Logging**: Authentication and authorization events
- **Performance Logging**: Request duration and response sizes

### Health Monitoring
- **Health Endpoints**: `/health`, `/v1/health`
- **Container Health Checks**: Docker health check integration
- **Database Connectivity**: MongoDB connection monitoring
- **Service Status**: Background service health tracking

### Analytics & User Tracking

#### Amplitude Analytics Integration
- **API Key**: e536f074ed1081e2d622a70005e3d9ad
- **Session Replay**: 100% sampling rate for user session recording
- **Autocapture**: Automatic tracking of clicks, form submissions, and interactions
- **Custom Events**: Business-specific event tracking (page views, user actions)
- **User Properties**: User role and behavior tracking
- **Fallback Handling**: Graceful degradation when analytics fails to load

#### Event Tracking Categories
```javascript
// Navigation Events
- Page Viewed (page_name, user_role, timestamp)
- Modal Opened (modal_name, timestamp)

// User Actions
- Form Submitted (form_type, success_status)
- Button Clicked (button_name, context)
- File Uploaded (file_type, file_size)

// Business Events
- Lease Created (property_id, tenant_count)
- Payment Processed (amount, payment_method)
- Report Generated (report_type, generation_time)
```

#### Analytics Architecture
- **Client-Side Tracking**: JavaScript-based event collection
- **Post-Login Only**: Analytics tracking only on authenticated pages
- **Privacy Compliant**: User consent and data protection measures
- **Performance Optimized**: Asynchronous loading with fallback mechanisms

### Error Handling
- **Graceful Degradation**: Service failure handling
- **Error Recovery**: Automatic retry mechanisms
- **Error Reporting**: Structured error logging
- **User Feedback**: User-friendly error messages

---

## CLI Tools & Automation Architecture

### Command Line Interface Tools

The system provides a comprehensive CLI toolkit for batch processing and administrative operations:

#### CLI Tools Overview
```
cli/
├── cli_auto_payment.go           # Daily automated payment processing
├── cli_debt_reminder.go          # Monthly debt reminder notifications
├── cli_metro2_auto_generation.go # Monthly Metro2 credit report generation
├── cli_metro2_notification.go    # Metro2 notification task processing
└── cli_rent_report_batch.go      # Rent report email batch processing
```

#### CLI Architecture Features
- **Unified Configuration**: All CLI tools use same INI configuration system
- **Consistent Logging**: Structured logging with golog library
- **Database Integration**: Direct MongoDB access with connection pooling
- **Error Handling**: Comprehensive error handling with exit codes
- **Environment Flexibility**: Support for multiple deployment environments

#### Shell Script Schedulers
```
scripts/
├── auto_payment_scheduler.sh        # Daily payment automation (cron: 0 9 * * *)
├── debt_reminder_scheduler.sh       # Monthly debt reminders (cron: 0 10 15 * *)
├── metro2_auto_generation.sh        # Monthly report generation (cron: 0 2 1 * *)
├── metro2_notification_scheduler.sh # Notification processing (on-demand)
└── rent_report_batch_scheduler.sh   # Email batch processing (cron: */30 * * * *)
```

#### Script Features
- **Robust Logging**: Timestamped logs with rotation
- **PID Management**: Process tracking and cleanup
- **Binary Validation**: Automatic CLI tool verification
- **Environment Setup**: Configuration and dependency checking
- **Error Recovery**: Graceful failure handling and reporting

### Test & Monitoring Infrastructure

#### Monitoring Tools
```
tests/
├── monitor_tenant_notifications.go  # Real-time tenant status monitoring
├── test_account_metro2_generation.go # Metro2 generation validation
├── test_email_simple.go            # Email system functionality testing
└── test_scenario*.go               # Comprehensive integration test suites
```

#### Monitoring Capabilities
- **Real-time Monitoring**: Live tracking of database changes
- **Performance Testing**: Load and stress testing utilities
- **Integration Validation**: End-to-end workflow testing
- **Data Integrity Checks**: Automated data validation and consistency checks

---

## Performance & Scalability

### Database Optimization
- **Indexing Strategy**: Compound indexes on query patterns
- **Connection Pooling**: Optimized MongoDB connections
- **Query Optimization**: Efficient aggregation pipelines
- **Data Archiving**: Automated cleanup of old records

### Caching Strategy
- **Static Asset Caching**: Long-term browser caching
- **API Response Caching**: Configurable response caching
- **Database Query Caching**: MongoDB query result caching

### Scalability Considerations
- **Horizontal Scaling**: Stateless application design
- **Load Balancing**: Nginx upstream configuration
- **Database Sharding**: MongoDB sharding support
- **Microservices Ready**: Modular architecture for service extraction

---

## Integration Architecture

### External Service Integration

#### Stripe Integration
- **Payment Processing**: Checkout sessions and payment intents
- **Subscription Management**: Recurring billing and plan changes
- **Webhook Security**: Mandatory signature verification
- **Event Handling**: Comprehensive webhook event processing
- **Error Handling**: Retry logic and failure notifications

#### Email Service Integration
- **SMTP Configuration**: Configurable email providers
- **Template System**: HTML email templates with variables
- **Batch Processing**: Queue-based email delivery
- **Delivery Tracking**: Success/failure logging
- **Rate Limiting**: Respect provider sending limits

#### OAuth2 Integration (RealMaster)
- **Authorization Code Flow**: Standard OAuth2 implementation
- **Token Management**: Access and refresh token handling
- **User Profile Sync**: Automatic user creation and updates
- **Security**: PKCE and state parameter validation

#### File Storage Integration
- **goupload Library**: Unified file upload interface
- **Multiple Backends**: Local filesystem, cloud storage ready
- **Security**: File type validation and virus scanning
- **Metadata Tracking**: File ownership and access control

### API Integration Patterns

#### RESTful API Design
- **Resource-Based URLs**: `/v1/properties/:id/rooms/:roomId`
- **HTTP Method Semantics**: GET (read), POST (create), PUT (update), DELETE (remove)
- **Status Code Standards**: 2xx success, 4xx client error, 5xx server error
- **Content Negotiation**: JSON primary, file downloads as binary
- **Versioning**: URL-based versioning (`/v1/`)

#### Webhook Handling
```go
// Stripe webhook signature verification
func verifyWebhookSignature(payload []byte, signature string) error {
    event, err := webhook.ConstructEventWithOptions(
        payload, signature, webhookSecret,
        webhook.ConstructEventOptions{
            IgnoreAPIVersionMismatch: true,
        },
    )
    return err
}
```

#### Async Processing Patterns
- **Background Jobs**: Scheduler-based task processing
- **Queue Management**: Database-backed job queues
- **Retry Logic**: Exponential backoff with max attempts
- **Dead Letter Queue**: Failed job handling and analysis

#### Circuit Breaker Implementation
- **Failure Detection**: Track error rates and response times
- **State Management**: Closed, Open, Half-Open states
- **Fallback Responses**: Graceful degradation strategies
- **Recovery Testing**: Automatic service health probing

### Data Flow Architecture
```
User Request → Middleware → Controller → Entity → Database
                ↓
Background Services → External APIs → Notifications
```

### Request Processing Flow
```
1. HTTP Request → Gin Router
2. Security Middleware → Authentication/Authorization
3. Controller → Business Logic Processing
4. Entity Layer → Data Validation & Business Rules
5. Database Layer → MongoDB Operations
6. Response → JSON/File/Redirect
```

### Background Processing Flow
```
1. Scheduler Trigger → Time-based or Event-based
2. Service Worker → Background Task Processing
3. Database Operations → Batch Processing
4. External API Calls → Email/Stripe/OAuth
5. Result Logging → Success/Failure Tracking
6. Cleanup → Resource Management
```

---

## Development Workflow

### Local Development Setup
```bash
# 1. Clone and setup
git clone <repository>
cd rent_report/src

# 2. Install dependencies
make setup

# 3. Configure environment
cp local.ini.example local.ini
# Edit configuration as needed

# 4. Start development
make start  # Builds frontend + starts backend
```

### Development Tools
- **Hot Reload**: Vite dev server for frontend changes
- **Live Reload**: Go air for backend changes (optional)
- **API Testing**: Built-in health endpoints and OpenAPI spec
- **Mock Data**: Frontend mock server for isolated development

### Code Quality
- **Linting**: Go fmt, vet, and custom linters
- **Testing**: Unit tests and integration tests
- **Documentation**: Automated API documentation generation
- **Security Scanning**: Dependency vulnerability scanning

---

## Business Logic Architecture

### Domain-Driven Design
The system follows domain-driven design principles with clear business domains:

**Property Management Domain**:
- Property creation and management
- Room management within properties
- Property document handling
- Property archiving and lifecycle

**Lease Management Domain**:
- Lease agreement creation and management
- Tenant relationship management
- Payment tracking and history
- Lease lifecycle (active, ended, archived)

**User Management Domain**:
- User registration and authentication
- Role-based access control
- Subscription management
- User limits and restrictions

**Financial Domain**:
- Payment processing and tracking
- Subscription billing
- Metro2 credit reporting
- Financial audit trails

### Business Rules Engine
- **User Limits**: Free users limited to 3 properties and 3 leases
- **Access Control**: Resource-based permissions with smart authentication
- **Data Validation**: Business rule validation at entity level
- **Audit Requirements**: Immutable records for compliance

---

## Error Handling & Recovery

### Error Classification
- **User Errors** (400-499): Invalid input, unauthorized access
- **System Errors** (500-599): Database failures, external service issues
- **Business Logic Errors**: Validation failures, business rule violations

### Recovery Strategies
- **Graceful Degradation**: Continue operation with reduced functionality
- **Retry Mechanisms**: Exponential backoff for transient failures
- **Circuit Breakers**: Prevent cascade failures from external services
- **Fallback Responses**: Default responses when services are unavailable

### Monitoring & Alerting
- **Error Rate Monitoring**: Track error rates and patterns
- **Performance Monitoring**: Response time and throughput tracking
- **Resource Monitoring**: Memory, CPU, and disk usage
- **Business Metrics**: User activity and system usage patterns

---

## Future Architecture Considerations

### Microservices Migration Path
The current monolithic architecture is designed to support future microservices extraction:

**Potential Service Boundaries**:
- User Service (authentication, profiles, subscriptions)
- Property Service (property and room management)
- Lease Service (lease lifecycle and tenant management)
- Payment Service (payment processing and financial tracking)
- Notification Service (email and communication)
- Reporting Service (Metro2 and analytics)

### Scalability Roadmap
- **Database Scaling**: MongoDB sharding and read replicas
- **Application Scaling**: Horizontal pod autoscaling
- **Caching Layer**: Redis for session and data caching
- **CDN Integration**: Static asset delivery optimization
- **Message Queue**: Async processing with message queues

### Technology Evolution
- **API Gateway**: Centralized API management and routing
- **Service Mesh**: Inter-service communication and observability
- **Event Sourcing**: Audit trail and state reconstruction
- **CQRS**: Command Query Responsibility Segregation for complex queries

---

## Architecture Benefits

### Scalability
- **Horizontal Scaling**: Stateless application design supports load balancing
- **Database Scaling**: MongoDB sharding and replica sets for high availability
- **Microservices Ready**: Modular architecture enables service extraction
- **Caching Strategy**: Multi-layer caching for performance optimization

### Security
- **Defense in Depth**: Multiple security layers from frontend to database
- **Zero Trust**: Every request validated and authorized
- **Data Protection**: Encryption at rest and in transit
- **Audit Trail**: Comprehensive logging for compliance and forensics

### Maintainability
- **Clean Architecture**: Clear separation of concerns and dependencies
- **Automated Testing**: Unit, integration, and end-to-end test support
- **Documentation**: Self-documenting code and comprehensive API docs
- **Code Quality**: Linting, formatting, and static analysis tools

### Reliability
- **Error Handling**: Graceful degradation and recovery mechanisms
- **Health Monitoring**: Comprehensive health checks and observability
- **Backup Strategy**: Database backups and disaster recovery
- **Deployment Safety**: Blue-green deployments and rollback capabilities

---

## Summary

The Rent Report system architecture represents a modern, well-structured approach to building scalable web applications. Key architectural decisions include:

1. **Monolithic Start**: Begin with a well-structured monolith that can evolve to microservices
2. **Security First**: Comprehensive security measures built into every layer
3. **Developer Experience**: Modern tooling and automated workflows
4. **Production Ready**: Container-based deployment with monitoring and observability
5. **Future Proof**: Architecture designed to support growth and technology evolution

## Architecture Summary

### Complete System Coverage

The Rent Report system architecture encompasses:

**Core Architecture Components**:
- ✅ **Frontend**: Vite + TailwindCSS multi-page application with HTMX
- ✅ **Backend**: Go + Gin RESTful API with 18 controllers
- ✅ **Database**: MongoDB with 24+ collections and optimized indexing
- ✅ **Authentication**: JWT with refresh tokens and OAuth2 integration
- ✅ **Security**: Comprehensive middleware stack with encryption and validation

**Advanced Features**:
- ✅ **Analytics**: Amplitude integration with session replay and event tracking
- ✅ **CLI Tools**: 5 command-line tools for batch processing and automation
- ✅ **Shell Scripts**: 5 scheduler scripts with cron integration
- ✅ **Background Services**: 5 automated schedulers for business processes
- ✅ **Monitoring**: Real-time monitoring tools and comprehensive testing suite

**Infrastructure & DevOps**:
- ✅ **Containerization**: Multi-stage Docker builds with Alpine Linux
- ✅ **Build System**: Make-based automation with cross-platform support
- ✅ **Configuration**: INI-based configuration with environment variable overrides
- ✅ **Deployment**: Container-based deployment with health checks
- ✅ **Scalability**: Microservices-ready architecture with clear service boundaries

**Security & Compliance**:
- ✅ **Data Protection**: AES-256 encryption for PII data
- ✅ **Access Control**: Role-based permissions with resource scoping
- ✅ **Security Middleware**: XSS, CSRF, CORS, and rate limiting protection
- ✅ **Audit Trail**: Comprehensive logging and security event tracking
- ✅ **Payment Security**: Stripe integration with mandatory webhook verification

**Performance & Monitoring**:
- ✅ **Database Optimization**: Compound indexing and connection pooling
- ✅ **Caching Strategy**: Multi-layer caching for static assets and API responses
- ✅ **Error Handling**: Circuit breakers, retry mechanisms, and graceful degradation
- ✅ **Observability**: Structured logging, health monitoring, and performance tracking

This comprehensive architecture provides a robust, scalable, and secure foundation for the rent management system with clear separation of concerns, modern development practices, advanced analytics capabilities, and complete automation infrastructure, while maintaining flexibility for future growth and evolution.
