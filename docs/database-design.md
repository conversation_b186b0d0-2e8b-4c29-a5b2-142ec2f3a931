# Database Design Document

## Overview

This document describes the MongoDB database design for the Rent Report application, a comprehensive property and lease management system for landlords and tenants.

## Database Architecture

- **Database Engine**: MongoDB 4.4+
- **Database Name**: `rr` (rent_report)
- **Connection**: Via `gomongo` driver
- **Collection Access**: `gomongo.Coll("rr", "collection_name")`
- **Indexing Strategy**: Compound indexes on frequently queried fields
- **Data Encryption**: AES-256 encryption for PII fields at application layer

### Database Configuration

**Configuration Files**:
- `src/local.ini` - Local development configuration
- `configs/rain.ini` - Production configuration
- `configs/local.ini` - Alternative local configuration

**Database Connection Setup**:
```ini
[dbs]
verbose = 3

[dbs.rr]
uri = "mongodb://r1:r1@198.19.249.3:27017/rr?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

[dbs.rr_test]
uri = "mongodb://r1:r1@198.19.249.3:27017/rr_test?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"
```

**Collection Access Pattern**:
All collections are accessed using the `gomongo.Coll("rr", "collection_name")` pattern where:
- `"rr"` is the database name prefix (configured in `[dbs.rr]`)
- `"collection_name"` is the actual collection name
- No additional collection prefixes are used in this system

**Initialization**:
```go
// Initialize MongoDB connection
err := gomongo.InitMongoDB()
if err != nil {
    golog.Fatal("Failed to initialize MongoDB", "error", err)
}

// Access collections
userColl := gomongo.Coll("rr", "users")
leaseColl := gomongo.Coll("rr", "leases")
```

## Collections Overview

The database consists of 22+ collections organized around core business entities:

**Collection Naming Convention**:
- Collections use simple, descriptive names without prefixes
- All collections are accessed via `gomongo.Coll("rr", "collection_name")`
- Database name `"rr"` serves as the namespace identifier
- No additional collection-level prefixes are implemented

### Core Business Collections
1. **users** - User accounts and authentication
2. **properties** - Property management
3. **leases** - Lease agreements and lifecycle
4. **tenants** - Tenant information (encrypted PII)
5. **tenant_payments** - Payment records and history

### Supporting Collections
6. **invitations** - Tenant invitation system
7. **problems** - Issue reporting and tracking
8. **referral_codes** - Referral code management
9. **metro2_reports** - Credit reporting data
10. **metro2_generation_logs** - Metro2 file generation history
11. **metro2_notification_tasks** - Delayed notification tasks

### System Collections
12. **admin_logs** - Administrative action logs
13. **rent_report_email_queue** - Email notification queue
14. **messages** - Message history
15. **subplans** - Subscription plans
16. **usersubs** - User subscriptions
17. **ordinfo** - Order information
18. **payinfo** - Payment method information
19. **billhst** - Billing history
20. **stripeeventlog** - Stripe webhook event logs

### Monitoring Collections
21. **tenant_status_snapshots** - Tenant status monitoring snapshots
22. **email_notification_queue** - Email notification queue for monitoring

### Special Collections
23. **referral_code_usages** - Referral code usage tracking
24. **mail_log** - Email delivery logs (TTL: 30 days)

---

## Detailed Collection Schemas

### 1. users Collection

**Purpose**: Store user accounts, authentication data, and profile information

```javascript
{
  _id: "nano_id_string",                    // Primary key (NanoID)
  email: "<EMAIL>",                // Unique email address
  usrNm: "john_doe",                        // Username (defaults to email)
  pwd: "$2a$10$...",                        // Bcrypt hashed password
  acctTp: "landlord|tenant",                // Account type
  role: "normal_user|admin|dev|super_admin", // User role
  status: "active|disabled|suspended",       // Account status
  orgId: "organization_id",                  // Organization ID (optional)
  
  // OAuth fields
  isOAuth: false,                           // OAuth account flag
  oauthId: "oauth_provider_id",             // OAuth provider ID
  
  // Verification fields
  isVerified: true,                         // Email verification status
  verifyCode: "123456",                     // Verification code
  codeExpiry: ISODate("2024-01-01T00:00:00Z"), // Code expiration
  
  // Password reset fields
  bindverifyCode: "654321",                 // Password reset code
  bindCodeExpiry: ISODate("2024-01-01T00:00:00Z"), // Reset code expiration
  pendingPassword: "$2a$10$...",            // Pending password hash
  
  // Profile fields
  phoneNumber: "+**********",               // Phone number
  address: {                                // Address object
    street: "123 Main St",
    city: "Toronto",
    prov: "ON",
    country: "Canada",
    zipCode: "M1M 1M1"
  },
  
  // UI preferences
  viewType: "landlord|tenant",              // Current view mode
  
  // Referral system
  rfrl_cd: "referral_code",                 // Applied referral code
  
  // Invitation lists
  tntInvLst: ["invitation_id1", "invitation_id2"], // Tenant invitations
  lndInvLst: ["invitation_id3", "invitation_id4"], // Landlord invitations
  
  // Stripe integration
  stripeCusIds: ["cus_stripe_id1", "cus_stripe_id2"] // Stripe customer IDs
}
```

**Indexes**:
- `{ email: 1 }` - Unique index for login
- `{ usrNm: 1 }` - Unique index for username
- `{ orgId: 1 }` - Organization queries
- `{ role: 1, status: 1 }` - Admin user management

**Security Notes**:
- Passwords are hashed using bcrypt with default cost (10)
- Verification codes expire after 24 hours
- OAuth users are automatically verified

---

### 2. properties Collection

**Purpose**: Store property information, rooms, and associated documents

```javascript
{
  _id: "nano_id_string",                    // Primary key
  nm: "Sunset Apartments",                  // Property name
  usrId: "user_id",                         // Owner/manager user ID
  orgId: "organization_id",                 // Organization ID (optional)
  invId: "invitation_id",                   // Related invitation ID
  
  // Address information
  addr: {
    street: "123 Sunset Blvd",
    unit: "Suite 100",                      // Unit number (optional)
    city: "Toronto",
    prov: "ON",                             // Province/State
    country: "Canada",
    zip: "M1M 1M1"                          // Postal/ZIP code
  },
  
  // Property details
  propTp: "apartment|house|condo|commercial", // Property type
  totRms: 10,                               // Total rooms
  vacRms: 2,                                // Vacant rooms
  stat: "active|inactive|archived",         // Property status
  notes: "Property notes and description",
  
  // Room information (embedded array)
  rooms: [
    {
      _id: "room_nano_id",                  // Room ID
      nm: "Unit 101",                       // Room name
      tp: "studio|1br|2br|3br|commercial",  // Room type
      stat: "vacant|occupied|maintenance",   // Room status
      notes: "Room-specific notes"
    }
  ],
  
  // Document management (embedded array)
  documents: [
    {
      _id: "doc_nano_id",                   // Document ID
      fileName: "lease_template.pdf",        // Original filename
      generatedName: "generated_name.pdf",   // System generated name
      filePath: "/uploads/property_documents/...", // File path
      fileSize: 1048576,                    // File size in bytes
      fileType: "application/pdf",          // MIME type
      uploadedAt: ISODate("2024-01-01T00:00:00Z"), // Upload timestamp
      uploadedBy: "user_id",                // Uploader user ID
      status: "active|deleted|final_deleted", // Document status
      deletedAt: ISODate("2024-01-01T00:00:00Z"), // Deletion timestamp
      isProtected: false                    // Protection flag (Metro2 usage)
    }
  ]
}
```

**Indexes**:
- `{ usrId: 1, stat: 1 }` - User property queries
- `{ orgId: 1 }` - Organization queries
- `{ "addr.city": 1, "addr.prov": 1 }` - Location-based searches
- `{ invId: 1 }` - Invitation-based queries

**Business Rules**:
- `vacRms` must be <= `totRms`
- Room status affects property `vacRms` count
- Documents marked as `isProtected` cannot be deleted
- Property status `archived` sets all associated leases to `ended`

---

### 3. leases Collection

**Purpose**: Store lease agreements, terms, and financial information

```javascript
{
  _id: "nano_id_string",                    // Primary key
  propId: "property_id",                    // Foreign key to properties
  roomId: "room_id",                        // Foreign key to property.rooms
  usrId: "user_id",                         // Landlord/manager user ID
  orgId: "organization_id",                 // Organization ID (optional)
  invId: "invitation_id",                   // Related invitation ID
  
  // Lease terms
  startDt: "2024-01-01",                    // Lease start date (YYYY-MM-DD)
  endDt: "2024-12-31",                      // Lease end date (YYYY-MM-DD)
  stat: "active|ended|terminated",          // Lease status
  
  // Financial terms
  rentAm: 1500.00,                          // Monthly rent amount
  addFees: 50.00,                           // Additional monthly fees
  keyDep: 100.00,                           // Key deposit
  rentDep: 1500.00,                         // Rent deposit
  otherDep: 200.00,                         // Other deposits
  
  // Payment configuration
  rentDueDay: 1,                            // Day of month rent is due (1-31)
  rentRep: true,                            // Rent reporting enabled
  autoPay: false,                           // Auto payment enabled
  owingBal: 0.00,                           // Current owing balance
  lastPmtDt: "2024-01-01",                  // Last payment date
  
  // Tenant relationships (embedded arrays)
  ctnts: [                                  // Current tenants
    {
      _id: "tenant_id",
      firstNm: "John",
      lastNm: "Doe",
      email: "<EMAIL>"
      // ... other tenant fields
    }
  ],
  ptnts: [                                  // Past tenants
    // Same structure as current tenants
  ],
  
  // Document management
  documents: [
    {
      _id: "doc_nano_id",
      fileName: "signed_lease.pdf",
      generatedName: "lease_generated.pdf",
      filePath: "/uploads/lease_documents/...",
      fileSize: 2097152,
      fileType: "application/pdf",
      uploadedAt: ISODate("2024-01-01T00:00:00Z"),
      uploadedBy: "user_id",
      status: "active|deleted|final_deleted",
      deletedAt: ISODate("2024-01-01T00:00:00Z"),
      isProtected: false
    }
  ],
  
  notes: "Lease notes and special terms"
}
```

**Indexes**:
- `{ usrId: 1, stat: 1 }` - User lease queries
- `{ propId: 1, roomId: 1 }` - Property-room queries
- `{ rentDueDay: 1, stat: 1, autoPay: 1 }` - Auto payment processing
- `{ "ctnts._id": 1 }` - Tenant-based queries
- `{ rentRep: 1, stat: 1 }` - Metro2 reporting queries
- `{ orgId: 1 }` - Organization queries

**Business Rules**:
- Lease dates must be valid (startDt < endDt)
- `rentDueDay` must be between 1-31
- `owingBal` can be negative (overpayment)
- Status `ended` triggers room status update to `vacant`
- Protected documents cannot be deleted if used in Metro2 reports

---

### 4. tenants Collection

**Purpose**: Store tenant personal information with encryption for PII

```javascript
{
  _id: "nano_id_string",                    // Primary key
  leaseId: "lease_id",                      // Foreign key to leases
  usrId: "user_id",                         // Landlord/manager user ID
  orgId: "organization_id",                 // Organization ID (optional)
  tenantId: "tenant_identifier",            // Business tenant ID

  // Personal information
  firstNm: "John",                          // First name
  midNm: "Michael",                         // Middle name (optional)
  lastNm: "Doe",                            // Last name
  email: "<EMAIL>",            // Email address

  // Encrypted PII fields
  phone: "encrypted_phone_data",            // Encrypted phone number
  sin: "encrypted_sin_data",                // Encrypted SIN number
  dob: "encrypted_dob_data",                // Encrypted date of birth

  notes: "Tenant notes and remarks",
  isProtected: false                        // Protection flag (Metro2 usage)
}
```

**Indexes**:
- `{ leaseId: 1 }` - Lease-tenant relationship
- `{ usrId: 1 }` - Landlord tenant queries
- `{ email: 1 }` - Email-based searches
- `{ tenantId: 1 }` - Business ID queries
- `{ orgId: 1 }` - Organization queries

**Encryption Details**:
- Phone numbers, SIN numbers, and dates of birth are encrypted using AES-256
- Encryption/decryption handled at application layer
- Search on encrypted fields requires special handling
- Backup procedures maintain encryption

**Business Rules**:
- Email must be unique within organization scope
- Protected tenants cannot be deleted if referenced in Metro2 reports
- Tenant type (current/past) determined by lease relationship

---

### 5. tenant_payments Collection

**Purpose**: Track all payment transactions and balance changes

```javascript
{
  _id: "nano_id_string",                    // Primary key
  leaseId: "lease_id",                      // Foreign key to leases
  usrId: "user_id",                         // Landlord/manager user ID
  orgId: "organization_id",                 // Organization ID (optional)

  // Payment details
  amt: 1500.00,                             // Payment amount
  dt: ISODate("2024-01-01T00:00:00Z"),      // Payment date
  notes: "Monthly rent payment",            // Payment notes
  remBal: 0.00,                             // Remaining balance after payment
  stat: "pending|completed|failed",         // Payment status

  isProtected: false                        // Protection flag (Metro2 usage)
}
```

**Indexes**:
- `{ leaseId: 1, dt: -1 }` - Lease payment history
- `{ usrId: 1, dt: -1 }` - User payment queries
- `{ dt: 1, stat: 1 }` - Date-based reporting
- `{ orgId: 1 }` - Organization queries

**Business Rules**:
- Payment amounts can be negative (adjustments)
- `remBal` reflects lease balance after this payment
- Auto payments have specific notes format
- Protected payments cannot be deleted if used in Metro2 reports

---

### 6. invitations Collection

**Purpose**: Manage tenant invitation system and lease setup

```javascript
{
  _id: "nano_id_string",                    // Primary key
  code: "invitation_code",                  // Unique invitation code
  senderId: "sender_user_id",               // Inviting user ID
  senderName: "John Landlord",              // Sender display name
  senderEmail: "<EMAIL>",      // Sender email

  // Recipient information
  receiverEmail: "<EMAIL>",      // Recipient email
  receiverName: "Jane Tenant",              // Recipient name
  receiverPhone: "+**********",             // Recipient phone

  // Property and lease details
  propertyName: "Sunset Apartments",        // Property name
  propertyAddress: "123 Sunset Blvd, Toronto", // Property address
  fromDate: "2024-01-01",                   // Lease start date
  toDate: "2024-12-31",                     // Lease end date
  rentAmount: 1500.00,                      // Monthly rent

  // Invitation metadata
  status: "pending|accepted|expired|cancelled", // Invitation status
  type: "tenant_invitation",                // Invitation type
  orgId: "organization_id",                 // Organization ID
  leaseId: "lease_id",                      // Associated lease ID

  // Timestamps
  createdAt: ISODate("2024-01-01T00:00:00Z"), // Creation time
  expiresAt: ISODate("2024-01-02T00:00:00Z")  // Expiration time (24h default)
}
```

**Indexes**:
- `{ code: 1 }` - Unique invitation code lookup
- `{ receiverEmail: 1, status: 1 }` - Recipient queries
- `{ senderId: 1, status: 1 }` - Sender invitation management
- `{ expiresAt: 1 }` - Cleanup expired invitations
- `{ orgId: 1 }` - Organization queries

**Business Rules**:
- Invitation codes are unique NanoIDs
- Default expiration is 24 hours from creation
- Accepted invitations create lease and tenant records
- Expired invitations are automatically cleaned up

---

### 7. problems Collection

**Purpose**: Issue reporting and customer support tracking

```javascript
{
  _id: "nano_id_string",                    // Primary key
  usrId: "user_id",                         // Reporting user ID
  orgId: "organization_id",                 // Organization ID (optional)

  // Problem details
  type: "bug|feature|feedback|other",       // Problem category
  subject: "Issue with payment processing", // Problem subject
  desc: "Detailed problem description",     // Problem description
  contactEmail: "<EMAIL>",         // Contact email
  stat: "pending|in_progress|resolved|closed", // Problem status

  // File attachment (optional)
  filePath: "/uploads/problem_reports/...", // File path
  fileName: "screenshot.png",               // Original filename
  generatedName: "generated_name.png",      // System generated name
  fileSize: 524288,                         // File size in bytes
  fileType: "image/png",                    // MIME type

  // Related entities (optional)
  leaseId: "lease_id",                      // Related lease
  propertyId: "property_id",                // Related property

  // Timestamps
  createdAt: "2024-01-01T00:00:00Z",        // Creation timestamp
  updatedAt: "2024-01-01T00:00:00Z"         // Last update timestamp
}
```

**Indexes**:
- `{ usrId: 1, stat: 1 }` - User problem queries
- `{ stat: 1, createdAt: -1 }` - Admin problem management
- `{ type: 1, stat: 1 }` - Problem categorization
- `{ orgId: 1 }` - Organization queries

**Business Rules**:
- File attachments have size limits (4MB for images, 12MB for documents)
- Problems can be linked to specific leases or properties
- Status transitions follow workflow rules
- Contact email may differ from user email

---

### 8. referral_codes Collection

**Purpose**: Manage referral code system for user acquisition

```javascript
{
  _id: "nano_id_string",                    // Primary key
  code: "ABC123",                           // 6-character referral code
  type: "DISCOUNT|TRIAL|BONUS",             // Referral type
  ttluse: 100,                              // Total usage limit
  rmnuse: 95,                               // Remaining usage count
  crtr_uid: "creator_user_id",              // Creator user ID (optional)
  dscp: "10% discount for new users",       // Description
  status: "ACTIVE|INACTIVE|EXPIRED",        // Code status
  exp: ISODate("2024-12-31T23:59:59Z")      // Expiration date
}
```

**Indexes**:
- `{ code: 1 }` - Unique code lookup
- `{ status: 1, exp: 1 }` - Active code queries
- `{ crtr_uid: 1 }` - Creator code management

**Business Rules**:
- Codes are 6-character alphanumeric NanoIDs
- `rmnuse` decrements on each successful application
- Expired or exhausted codes become inactive
- Codes can be created by admins or specific users

---

### 9. metro2_generation_logs Collection

**Purpose**: Track Metro2 file generation history and metadata

```javascript
{
  _id: "nano_id_string",                    // Primary key
  usrId: "user_id",                         // Generating user ID
  orgId: "organization_id",                 // Organization ID (optional)
  reportMonth: "2024-01",                   // Report month (YYYY-MM)

  // Generation metadata
  generatedAt: ISODate("2024-02-01T10:00:00Z"), // Generation timestamp
  totalLeases: 25,                          // Total leases processed
  totalTenants: 30,                         // Total tenants processed

  // File information
  fileName: "Metro2-2024-01.txt",           // Generated filename
  fileSize: 10240,                          // File size in bytes
  fileId: "gridfs_file_id",                 // GridFS file ID (deprecated)
  jsonDataSize: 8192,                       // JSON data size
  jsonBackupPath: "/uploads/metro2_reports/backup.json", // JSON backup path
  metro2FilePath: "/uploads/metro2_reports/report.txt",  // Metro2 file path

  // Processing details
  leaseIds: ["lease1", "lease2", "lease3"], // Processed lease IDs
  tenantIds: ["tenant1", "tenant2", "tenant3"], // Processed tenant IDs

  // Date range
  startDate: "2024-01-01",                  // Report start date
  endDate: "2024-01-31"                     // Report end date
}
```

**Indexes**:
- `{ usrId: 1, reportMonth: -1 }` - User generation history
- `{ reportMonth: 1 }` - Monthly report queries
- `{ generatedAt: -1 }` - Recent generations
- `{ orgId: 1 }` - Organization queries

**Business Rules**:
- One generation log per user per month
- Files stored using goupload system
- JSON backup maintained for data recovery
- Used for Metro2 notification scheduling

---

### 10. metro2_notification_tasks Collection

**Purpose**: Manage delayed notification tasks for Metro2 reporting

```javascript
{
  _id: "nano_id_string",                    // Primary key
  usrId: "user_id",                         // User ID
  orgId: "organization_id",                 // Organization ID (optional)
  metro2GenerationId: "generation_log_id",  // Related generation log
  reportMonth: "2024-01",                   // Report month (YYYY-MM)

  // Scheduling
  scheduledTime: ISODate("2024-02-03T10:00:00Z"), // Execution time
  createdAt: ISODate("2024-02-01T10:00:00Z"),     // Creation time
  executedAt: ISODate("2024-02-03T10:05:00Z"),    // Execution time (optional)

  // Execution status
  completed: false,                         // Completion flag
  emailsSent: 0,                            // Number of emails sent
  errorMessage: "Error details if failed"   // Error message (optional)
}
```

**Indexes**:
- `{ scheduledTime: 1, completed: 1 }` - Task scheduling
- `{ metro2GenerationId: 1 }` - Generation-based queries
- `{ usrId: 1, reportMonth: 1 }` - User task queries
- `{ orgId: 1 }` - Organization queries

**Business Rules**:
- Tasks scheduled with configurable delay after Metro2 generation
- Sends debt notification emails to landlords
- Automatic cleanup of completed tasks after retention period
- Error handling and retry logic for failed tasks

---

### 11. admin_logs Collection

**Purpose**: Audit trail for administrative actions

```javascript
{
  _id: "nano_id_string",                    // Primary key
  act: "user_update|user_delete|system_config", // Action type
  chg: "Updated user role from user to admin",   // Change description
  userId: "target_user_id",                 // Target user ID
  adminId: "admin_user_id",                 // Performing admin ID
  ts: ISODate("2024-01-01T10:00:00Z")       // Timestamp
}
```

**Indexes**:
- `{ adminId: 1, ts: -1 }` - Admin action history
- `{ userId: 1, ts: -1 }` - User-specific changes
- `{ act: 1, ts: -1 }` - Action type queries
- `{ ts: -1 }` - Recent activity

**Business Rules**:
- Immutable audit records
- All administrative actions must be logged
- Retention policy for log cleanup
- Used for compliance and security monitoring

---

### 12. rent_report_email_queue Collection

**Purpose**: Queue system for rent reporting notification emails

```javascript
{
  _id: "tenant_id_timestamp",               // Composite primary key
  tntId: "tenant_id",                       // Tenant ID (unique identifier)
  tntEmail: "<EMAIL>",           // Tenant email
  tntFirstName: "Jane",                     // Tenant first name
  leaseId: "lease_id",                      // Associated lease ID
  propId: "property_id",                    // Associated property ID
  landlordId: "landlord_user_id",           // Landlord user ID

  // Status tracking
  initialStatus: "inactive",                // Initial rent reporting status
  finalStatus: "active",                    // Final rent reporting status
  processed: false,                         // Processing flag

  // Timestamps
  lastUpdated: ISODate("2024-01-01T10:00:00Z"), // Last update time
  createdAt: ISODate("2024-01-01T10:00:00Z")    // Creation time
}
```

**Indexes**:
- `{ tntId: 1, processed: 1 }` - Tenant processing queries
- `{ processed: 1, lastUpdated: 1 }` - Batch processing
- `{ landlordId: 1 }` - Landlord-based queries
- `{ createdAt: 1 }` - Cleanup queries

**Business Rules**:
- Deduplication based on tenant ID
- Batch processing with configurable intervals
- Status change tracking for email content
- Automatic cleanup of processed records

---

### 13. messages Collection

**Purpose**: Store message history for communication tracking

```javascript
{
  _id: "nano_id_string",                    // Primary key
  from: "<EMAIL>",               // Sender email
  to: ["<EMAIL>", "<EMAIL>"], // Recipients
  toName: ["John Doe", "Jane Smith"],       // Recipient names
  recipientLeaseIds: ["lease1", "lease2"],  // Associated lease IDs
  recipientLeaseStatuses: ["active", "ended"], // Lease statuses
  subject: "Monthly rent reminder",         // Message subject
  body: "Message body content",             // Message body
  sentAt: ISODate("2024-01-01T10:00:00Z")   // Send timestamp
}
```

**Indexes**:
- `{ from: 1, sentAt: -1 }` - Sender message history
- `{ to: 1, sentAt: -1 }` - Recipient queries
- `{ sentAt: -1 }` - Recent messages
- `{ recipientLeaseIds: 1 }` - Lease-based queries

**Business Rules**:
- Messages stored after successful email delivery
- Support for multiple recipients
- Associated with lease context for tracking
- Used for communication audit trail

---

## Subscription and Payment Collections

### 14. subplans Collection

**Purpose**: Define subscription plans and pricing

```javascript
{
  _id: "plan_id",                           // Primary key (Plan ID)
  nm: "VIP Monthly",                        // Plan name
  prc: 999,                                 // Price in cents
  cur: "CAD",                               // Currency
  intrvl: "month|year",                     // Billing interval
  dscp: "VIP features with monthly billing", // Description
  prdId: "stripe_product_id",               // Stripe Product ID
  prcId: "stripe_price_id",                 // Stripe Price ID
  sts: "active|inactive",                   // Plan status
  ts: ISODate("2024-01-01T00:00:00Z"),      // Created timestamp
  mt: ISODate("2024-01-01T00:00:00Z")       // Modified timestamp
}
```

### 15. usersubs Collection

**Purpose**: Track user subscription relationships

```javascript
{
  _id: "stripe_subscription_id",            // Primary key (Stripe Sub ID)
  uid: "user_id",                           // User ID
  planId: "plan_id",                        // Subscription plan ID
  sts: "active|canceled|trial|past_due",    // Subscription status
  ts: ISODate("2024-01-01T00:00:00Z"),      // Created timestamp
  mt: ISODate("2024-01-01T00:00:00Z")       // Modified timestamp
}
```

### 16. ordinfo Collection

**Purpose**: Store order information for purchases

```javascript
{
  _id: "order_id",                          // Primary key (Order ID)
  uid: "user_id",                           // User ID
  prdId: "product_id",                      // Product/Service ID
  qty: 1,                                   // Quantity
  ttlAmt: 999,                              // Total amount in cents
  cur: "CAD",                               // Currency
  sts: "paid|pending|failed",               // Order status
  ssnId: "stripe_session_id",               // Stripe Checkout Session ID
  ts: ISODate("2024-01-01T00:00:00Z"),      // Created timestamp
  mt: ISODate("2024-01-01T00:00:00Z")       // Modified timestamp
}
```

### 17. payinfo Collection

**Purpose**: Store payment method information

```javascript
{
  _id: "payment_id",                        // Primary key (Payment ID)
  uid: "user_id",                           // User ID
  cusId: "stripe_customer_id",              // Stripe Customer ID
  pmId: "stripe_payment_method_id",         // Payment Method ID
  crdBrd: "visa",                           // Card brand
  crdL4: "4242",                            // Last 4 digits
  expMn: 12,                                // Expiry month
  expYr: 2025,                              // Expiry year
  mtdTp: "card",                            // Payment method type
  billingAddress: {                         // Billing address
    line1: "123 Main St",
    city: "Toronto",
    state: "ON",
    postal_code: "M1M 1M1",
    country: "CA"
  },
  ts: ISODate("2024-01-01T00:00:00Z")       // Created timestamp
}
```

### 18. billhst Collection

**Purpose**: Billing history and payment records

```javascript
{
  _id: "billing_id",                        // Primary key (Billing ID)
  uid: "user_id",                           // User ID
  cusId: "stripe_customer_id",              // Stripe Customer ID
  amt: 999,                                 // Amount in cents
  cur: "CAD",                               // Currency
  sts: "succeeded|failed|pending",          // Payment status
  pid: "payment_intent_id",                 // Payment Intent ID
  pmId: "payment_method_id",                // Payment Method ID
  crdL4: "4242",                            // Last 4 digits of card
  invId: "invoice_id",                      // Invoice ID (optional)
  dscp: "VIP Monthly Subscription",         // Description
  ts: ISODate("2024-01-01T00:00:00Z"),      // Created timestamp
  mt: ISODate("2024-01-01T00:00:00Z")       // Modified timestamp
}
```

### 19. stripeeventlog Collection

**Purpose**: Stripe webhook event logging

```javascript
{
  _id: "stripe_event_id",                   // Primary key (Stripe Event ID)
  tp: "payment_intent.succeeded",           // Event type
  sts: "received|processed|failed",         // Processing status
  raw: "{ ... }",                           // Raw JSON payload (optional)
  pid: "payment_id",                        // Related payment ID (optional)
  ts: ISODate("2024-01-01T00:00:00Z"),      // Received timestamp
  mt: ISODate("2024-01-01T00:00:00Z")       // Modified timestamp (optional)
}
```

---

## Data Relationships

### Key Relationships

1. **User → Properties → Leases → Tenants**
   - Hierarchical ownership model
   - User owns properties, properties contain leases, leases house tenants

2. **Lease → Payments**
   - One-to-many relationship
   - Payments update lease owing balance

3. **Tenant → Email Queue**
   - Status changes trigger email notifications
   - Deduplication based on tenant ID

4. **Metro2 Generation → Notification Tasks**
   - Generation triggers delayed notification scheduling
   - Tasks process debt notifications to landlords

5. **User → Subscriptions → Billing**
   - Subscription management through Stripe integration
   - Payment history and billing records

---

## Indexing Strategy

### Primary Indexes

All collections use efficient primary keys:
- **NanoID strings** for business entities (users, properties, leases, etc.)
- **Composite keys** for queue systems (tenant_id + timestamp)
- **External IDs** for integration entities (Stripe IDs)

### Compound Indexes

Critical compound indexes for performance:

```javascript
// User-scoped queries
{ usrId: 1, stat: 1 }           // Properties, leases by user and status
{ usrId: 1, dt: -1 }            // Payments by user, recent first

// Organization queries
{ orgId: 1, usrId: 1 }          // Multi-tenant organization support

// Date-based queries
{ dt: 1, stat: 1 }              // Payments by date and status
{ scheduledTime: 1, completed: 1 } // Task scheduling

// Business logic indexes
{ rentDueDay: 1, stat: 1, autoPay: 1 } // Auto payment processing
{ rentRep: 1, stat: 1 }         // Metro2 reporting queries
{ processed: 1, lastUpdated: 1 } // Queue processing
```

### Unique Indexes

Data integrity constraints:
```javascript
{ email: 1 }                    // User email uniqueness
{ code: 1 }                     // Referral code uniqueness
{ tntId: 1, processed: 1 }      // Email queue deduplication
```

---

## Data Security and Encryption

### Encryption at Rest

**PII Fields Encrypted**:
- `tenants.phone` - Phone numbers
- `tenants.sin` - Social Insurance Numbers
- `tenants.dob` - Dates of birth

**Encryption Method**:
- Algorithm: AES-256-GCM
- Key management: Environment variables
- Application-layer encryption/decryption
- Deterministic encryption for searchable fields

### Access Control

**Role-Based Access**:
- `normal_user` - Own data only
- `admin` - Organization data
- `super_admin` - All data
- `dev` - Development access

**Resource Scoping**:
- Organization-based data isolation
- User-based ownership validation
- Smart resource authentication middleware

### Audit Trail

**Administrative Actions**:
- All admin operations logged in `admin_logs`
- Immutable audit records
- User action tracking

**Data Changes**:
- Soft deletion with timestamps
- Status change tracking
- Document protection flags

---

## Performance Considerations

### Query Optimization

**Efficient Queries**:
- Use compound indexes for multi-field queries
- Limit result sets with pagination
- Project only required fields
- Use aggregation pipelines for complex operations

**Avoid**:
- Full collection scans
- Regex queries on large collections
- Unindexed sort operations
- Large document updates

### Data Size Management

**Document Size Limits**:
- Keep embedded arrays reasonable (<100 items)
- Use references for large related data
- Implement document archiving strategies

**Collection Maintenance**:
- Regular index analysis and optimization
- Automated cleanup of expired records
- Data archiving for historical records

### Caching Strategy

**Application-Level Caching**:
- User session data
- Frequently accessed property/lease data
- Metro2 generation results

**Database-Level Optimization**:
- Connection pooling
- Read preferences for reporting queries
- Write concerns for critical operations

---

## Backup and Recovery

### Backup Strategy

**Full Backups**:
- Daily automated backups
- Point-in-time recovery capability
- Encrypted backup storage

**Incremental Backups**:
- Hourly incremental backups
- Binary log shipping
- Cross-region replication

### Data Recovery

**Recovery Procedures**:
- Automated recovery testing
- RTO: 4 hours maximum
- RPO: 1 hour maximum

**Disaster Recovery**:
- Multi-region deployment
- Automated failover procedures
- Data consistency validation

---

## Compliance and Governance

### Data Privacy

**PIPEDA Compliance**:
- PII encryption at rest and in transit
- Data retention policies
- User consent tracking
- Right to deletion implementation

**Data Classification**:
- Public data (property listings)
- Internal data (business metrics)
- Confidential data (financial records)
- Restricted data (encrypted PII)

### Data Governance

**Data Quality**:
- Validation rules and constraints
- Data cleansing procedures
- Duplicate detection and resolution
- Consistency checks across collections

**Access Governance**:
- Role-based access control
- Regular access reviews
- Privileged access monitoring
- Data access audit trails

---

This database design provides a robust foundation for the Rent Report application, supporting complex property and lease management workflows while maintaining data security, performance, and scalability requirements.
