# Rent Report

租金报告系统 - 一个用于管理租赁物业、租户和租金报告的综合平台。

## 项目结构

```
rent_report/
├── src/                    # 后端源代码
│   ├── api/               # API 路由定义
│   ├── auth/              # 认证相关
│   ├── config/            # 配置管理
│   ├── controller/        # 控制器
│   ├── entities/          # 数据实体
│   ├── middleware/        # 中间件
│   ├── models/            # 数据模型
│   ├── router/            # 路由配置
│   ├── scheduler/         # 定时任务
│   ├── services/          # 业务服务
│   ├── utils/             # 工具函数
│   ├── web/               # 前端资源
│   │   ├── pages/         # 页面文件
│   │   ├── assets/        # 静态资源
│   │   ├── components/    # 组件
│   │   └── styles/        # 样式文件
│   ├── vendor/            # Go 依赖包（vendor 模式）
│   ├── main.go            # 主程序入口
│   ├── go.mod             # Go 模块依赖
│   ├── go.sum             # Go 依赖校验
│   ├── local.ini          # 本地配置文件
│   └── Makefile           # 构建脚本
├── cli/                   # 命令行工具
├── scripts/               # Shell 脚本和调度器
├── tests/                 # 测试文件
├── docs/                  # 技术文档
├── configs/               # 配置文件模板
├── design/                # 前端设计稿
└── build/                 # 构建输出目录
```

## 技术栈

### 后端
- **Go 1.24+** - 主要编程语言
- **Gin** - Web 框架
- **MongoDB** - 数据库
- **JWT** - 身份认证
- **Stripe** - 支付处理
- **OAuth2** - 第三方登录（RealMaster）
- **Metro2** - 信用报告格式

### 前端
- **Vite** - 现代构建工具
- **TailwindCSS** - 原子化 CSS 框架
- **DaisyUI** - UI 组件库
- **HTMX** - 动态交互
- **Mustache** - 模板引擎
- **jQuery** - DOM 操作

### 开发工具
- **Makefile** - 构建自动化
- **Docker** - 容器化部署
- **Podman** - 容器运行时
- **Shell Scripts** - 任务调度

## 环境要求

### 开发环境
- **Go**: 1.24+ （推荐 1.24.5）
- **Node.js**: 18+ （推荐 LTS 版本）
- **npm**: 9+
- **MongoDB**: 4.4+ （推荐 5.0+）

### 生产环境
- **Linux**: CentOS 7+, Ubuntu 18.04+, Alpine Linux
- **Docker**: 20.10+ 或 Podman 3.0+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 10GB 可用空间

## 快速开始

### 1. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd rent_report

# 初始化项目（推荐方式）
cd src
make setup
```

`make setup` 会自动完成：
- 安装 Go 依赖
- 安装 Node.js 依赖
- 构建前端资源
- 验证环境配置

### 2. 启动应用（推荐方式）

```bash
# 进入 src 目录
cd src

# 启动应用（完整流程）
make start
```

`make start` 会自动完成：
1. 安装前端依赖 (`npm install`)
2. 构建前端资源 (`npm run build`)
3. 启动后端服务器
4. 应用将在 http://localhost:8089 可用

### 3. 开发模式启动

```bash
# 方式1：完整开发环境（前端热重载 + 后端）
make dev-full
# 前端开发服务器: http://localhost:3000
# 后端 API 服务器: http://localhost:8089

# 方式2：仅启动后端（需要先构建前端）
make web-build    # 构建前端
make dev-backend  # 启动后端

# 方式3：仅启动前端开发服务器
make web-dev      # 启动前端热重载服务器
```

### 4. 传统启动方式（手动）

如果不使用 Makefile，可以手动启动：

```bash
# 前端
cd src/web
npm install
npm run build    # 生产构建
# 或
npm run dev      # 开发模式（热重载）

# 后端
cd src
go mod download
go mod vendor    # 创建 vendor 目录
go run main.go --config local.ini
```

## 配置说明

### 数据库配置

编辑 `src/local.ini` 文件：

```ini
[dbs.rr]
uri = "********************************:port/database?authSource=admin&replicaSet=rs0"
```

### Stripe 支付配置

Stripe 配置已集成在 `src/local.ini` 中，无需额外配置：

```ini
[stripe]
secret_key = "sk_test_..."        # Stripe 密钥
webhook_secret = "whsec_..."      # Webhook 验证密钥
```

**注意**: 生产环境请替换为正式的 Stripe 密钥。

### OAuth2 配置

RealMaster OAuth2 配置：

```ini
[oauthClient]
clientId = "report_rentals"
clientSecret = "your_client_secret"
redirectUrl = "http://localhost:8089/v1/auth/oauth/callback"
authUrl = "https://d8w.realmaster.com/provider/authorize"
tokenUrl = "https://d8w.realmaster.com/provider/token"
userInfoUrl = "https://d8w.realmaster.com/provider/userinfo"
```

### 邮件服务配置

支持多种邮件服务：

```ini
# Gmail SMTP
[mailEngine.gmail]
service = "gmail"
from = "Real Master Info <<EMAIL>>"
defaultEmail = "<EMAIL>"

[mailEngine.gmail.auth]
user = "<EMAIL>"
pass = "your_app_password"
host = "smtp.gmail.com"
port = 587
```

## 构建和部署

### 使用 Makefile（推荐）

```bash
# 查看所有可用命令
make help

# 完整构建（生产环境）
make full-build

# Docker 构建和部署
make docker-latest    # 构建 Docker 镜像
make docker-start     # 启动 Docker 容器

# 清理构建文件
make clean
```

### 手动构建

```bash
# 1. 构建前端
cd src/web
npm install
npm run build

# 2. 构建后端
cd ../
go mod download
go mod vendor          # 创建 vendor 目录
go build -o ../build/bin/rent_report main.go

# 3. 构建 CLI 工具
go build -o ../cli_auto_payment cli/cli_auto_payment.go
go build -o ../cli_debt_reminder cli/cli_debt_reminder.go
go build -o ../cli_metro2_auto_generation cli/cli_metro2_auto_generation.go
go build -o ../cli_metro2_notification cli/cli_metro2_notification.go
go build -o ../cli_rent_report_batch cli/cli_rent_report_batch.go
```

### Docker 部署

```bash
# 构建 Docker 镜像
make docker-latest

# 运行容器
docker run -d --name rent_report \
  -p 8089:8089 \
  -v $(pwd)/configs:/app/configs \
  rent_report:latest

# 查看容器日志
docker logs -f rent_report
```

### 生产环境部署

```bash
# 1. 构建生产版本
make full-build

# 2. 复制文件到服务器
scp -r build/* user@server:/opt/rent_report/

# 3. 在服务器上启动
cd /opt/rent_report
./bin/rent_report --config configs/local.ini
```

### 传统部署方式（兼容旧版本）

如果需要使用传统方式部署：

#### 前端部署
1. 进入 `src/web` 目录
2. 方法1: 运行 `npm run build`，然后移动 `dist` 文件夹到所需位置，修改 nginx 配置文件，将静态文件指向 `dist/index.html`
3. 方法2: 不更改 nginx 配置，使用目前 appweb 配置：
   - 将 `vite.config.js` 的 `BASE_URL` 和 `main.js` 的 `baseUrl` 修改为 `/web`
   - 运行 `npm run build`
   - 将 `dist` 目录下的所有文件放到原来 appweb 静态网页的位置：`cp -r dist/* /opt/appd9/appweb/src/webroot/public/web/`

#### 后端部署
1. 进入 `src` 目录
2. 修改 `local.ini`：
   ```ini
   [server]
   port = "8089"
   baseUrl = "/web"  ## 不更改nginx配置, 使用目前appweb配置
   ```
3. 编译和运行：
   ```bash
   go build  # 如果遇到问题请参考故障排除部分
   ./rent_report --config local.ini
   ```

### Go 版本兼容性处理

如果遇到 Go 版本不匹配问题：

```bash
# 创建私有模块目录
cd rent_report
mkdir private-modules
cd private-modules

# 克隆依赖模块
<NAME_EMAIL>:real-rm/goconfig
<NAME_EMAIL>:real-rm/golog
<NAME_EMAIL>:real-rm/gomongo
<NAME_EMAIL>:real-rm/gomail
<NAME_EMAIL>:real-rm/go-toml

# 修改每个模块的 go.mod 文件，设置为目标 Go 版本
# 例如：go 1.22.9

# 在 src/go.mod 中添加 replace 指令
cd ../src
vim go.mod
```

添加以下内容到 `go.mod`：
```go
replace (
    github.com/real-rm/goconfig => ../private-modules/goconfig
    github.com/real-rm/golog => ../private-modules/golog
    github.com/real-rm/gomongo => ../private-modules/gomongo
    github.com/real-rm/gomail => ../private-modules/gomail
    github.com/real-rm/go-toml => ../private-modules/go-toml
)
```

## CLI 工具

项目包含 5 个命令行工具，用于批处理和定时任务：

```bash
# 构建所有 CLI 工具
cd src
make cli-build

# 或手动构建
go build -o ../cli_auto_payment cli/cli_auto_payment.go
go build -o ../cli_debt_reminder cli/cli_debt_reminder.go
go build -o ../cli_metro2_auto_generation cli/cli_metro2_auto_generation.go
go build -o ../cli_metro2_notification cli/cli_metro2_notification.go
go build -o ../cli_rent_report_batch cli/cli_rent_report_batch.go
```

### CLI 工具说明

1. **cli_auto_payment** - 自动支付处理
2. **cli_debt_reminder** - 欠款提醒邮件
3. **cli_metro2_auto_generation** - Metro2 报告自动生成
4. **cli_metro2_notification** - Metro2 通知邮件
5. **cli_rent_report_batch** - 租金报告批处理

## 定时任务

项目包含 5 个 Shell 脚本用于 Cron 定时任务：

```bash
# 脚本位置：scripts/
├── auto_payment_scheduler.sh          # 自动支付调度器
├── debt_reminder_scheduler.sh         # 欠款提醒调度器
├── metro2_auto_generation.sh          # Metro2 自动生成
├── metro2_notification_scheduler.sh   # Metro2 通知调度器
└── rent_report_batch_scheduler.sh     # 租金报告批处理调度器
```

### Cron 配置示例

```bash
# 编辑 crontab
crontab -e

# 添加定时任务
0 1 * * * /path/to/rent_report/scripts/auto_payment_scheduler.sh
0 9 21 * * /path/to/rent_report/scripts/metro2_auto_generation.sh
0 10 15 * * /path/to/rent_report/scripts/debt_reminder_scheduler.sh
*/3 * * * * /path/to/rent_report/scripts/rent_report_batch_scheduler.sh
*/1 * * * * /path/to/rent_report/scripts/metro2_notification_scheduler.sh
```

## 测试

### 运行测试

```bash
# 运行所有测试
cd src
make test

# 运行集成测试
cd ../tests
go test ./...

# 运行特定测试
go test -v test_scenario1.go
```

### 测试文件说明

项目包含 20+ 个测试文件，涵盖：
- API 接口测试
- 业务场景测试
- 安全性测试
- 文件操作测试
- 集成测试

## 监控和日志

### 日志查看

```bash
# 查看应用日志
tail -f src/logs/*.log

# 查看特定日志
tail -f src/logs/info.log
tail -f src/logs/error.log
tail -f src/logs/verbose.log
```

### 监控脚本

```bash
# 监控租户通知
go run tests/monitor_tenant_notifications.go

# 监控系统状态
make monitor
```

## 故障排除

### 常见问题

1. **编译错误**: `gcc: error: unrecognized command-line option '-m64'`
   ```bash
   export CGO_ENABLED=0
   ```

2. **Go 版本不匹配**:
   ```bash
   # 检查版本
   go version

   # 使用 vendor 模式
   go mod vendor
   ```

3. **前端构建失败**:
   ```bash
   cd src/web
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

4. **数据库连接失败**:
   - 检查 MongoDB 服务状态
   - 验证 `src/local.ini` 中的连接字符串
   - 确认网络连接和防火墙设置

### 调试模式

```bash
# 启用详细日志
export GIN_MODE=debug

# 启用开发者模式
# 在 local.ini 中设置
[server]
developer_mode = true
```

## 功能特性

- **用户管理**: 注册、登录、OAuth2 集成
- **物业管理**: 物业信息、房间管理、文档上传
- **租赁管理**: 租约创建、租户管理、租金计算
- **支付处理**: Stripe 集成、自动支付、账单管理
- **信用报告**: Metro2 格式报告生成和上报
- **通知系统**: 邮件通知、欠款提醒、报告通知
- **文档管理**: 文件上传、下载、权限控制
- **管理面板**: 用户管理、数据统计、系统监控
- **API 接口**: RESTful API、Webhook 支持
- **安全机制**: JWT 认证、RBAC 权限、数据加密

## 技术文档

项目包含完整的技术文档：

- [数据库设计](docs/database-design.md) - 完整的数据库架构和集合设计
- [API 设计](docs/api-design.md) - 所有 API 接口的详细说明
- [系统架构](docs/system-architecture.md) - 系统整体架构和组件设计
- [流程设计](docs/process-batch-design.md) - 业务流程和批处理设计

## 开发指南

### 代码规范

- Go 代码遵循 `gofmt` 标准
- 前端代码使用 Prettier 格式化
- 提交信息使用英文，格式：`type: description`

### 开发流程

1. 创建功能分支
2. 开发和测试
3. 提交代码审查
4. 合并到主分支
5. 部署到测试环境

### 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用私有许可证，仅供授权用户使用。

---

**联系方式**: 如有问题请联系开发团队。