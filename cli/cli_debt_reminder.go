package main

import (
	"context"
	"fmt"
	"os"
	"rent_report/entities"
	"rent_report/utils"
	"rent_report/utils/encryption"
	"strings"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// Set default config file if none specified
	if os.Getenv("RMBASE_FILE_CFG") == "" {
		os.Setenv("RMBASE_FILE_CFG", "local.ini")
	}

	// Initialize configuration
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		fmt.Printf("Failed to initialize logging: %v\n", err)
		os.Exit(1)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		os.Exit(1)
	}

	// Initialize encryption
	encryption.InitEncryption()

	fmt.Println("Starting debt reminder processing...")

	// 检查配置是否启用
	enabled := goconfig.Config("debtReminderEmail.enabled")
	if enabled == nil || !enabled.(bool) {
		fmt.Println("Debt reminder is disabled")
		return
	}

	// 获取配置的执行日期
	sendDay := 15 // 默认15号
	if day := goconfig.Config("debtReminderEmail.sendDay"); day != nil {
		if dayInt, ok := day.(int); ok {
			sendDay = dayInt
		}
	}

	// 检查当前时间是否匹配执行条件
	now := time.Now()
	if now.Day() != sendDay {
		fmt.Printf("Not the scheduled time. Current: %d day. Scheduled: %d day\n",
			now.Day(), sendDay)
		return
	}

	fmt.Printf("Scheduled time matched! Executing debt reminder...\n")

	// 执行债务提醒任务
	if err := processDebtReminderTask(); err != nil {
		golog.Error("Debt reminder failed", "error", err)
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Debt reminder completed successfully")
}

// processDebtReminderTask 处理欠款提醒任务
func processDebtReminderTask() error {
	ctx := context.Background()

	golog.Info("Processing debt reminder task")

	// 获取所有有欠款的租户（已按房东分组）
	golog.Info("Searching for tenants with debt")

	tenantsByLandlord, landlordInfoMap, err := getTenantsWithDebt(ctx)
	if err != nil {
		return fmt.Errorf("failed to get tenants with debt: %v", err)
	}

	if len(tenantsByLandlord) == 0 {
		golog.Info("No tenants with debt found for reminder emails")
		return nil
	}

	golog.Info("Found tenants with debt", "landlordCount", len(tenantsByLandlord))

	// 直接遍历已分组的租户发送邮件
	totalEmailsSent := 0
	for landlordId, landlordTenants := range tenantsByLandlord {
		landlordInfo := landlordInfoMap[landlordId]
		landlordName := fmt.Sprintf("%s %s", landlordInfo.FirstName, landlordInfo.LastName)

		golog.Info("Sending debt reminder emails",
			"landlordId", landlordId,
			"landlordName", landlordName,
			"tenantCount", len(landlordTenants))

		// 发送邮件
		utils.SendDebtReminderEmailsAsync(landlordTenants, landlordInfo)
		totalEmailsSent += len(landlordTenants)
	}

	golog.Info("Completed debt reminder task", "totalEmailsSent", totalEmailsSent)
	return nil
}

// getTenantsWithDebt 获取所有有欠款的租户，按房东分组
func getTenantsWithDebt(ctx context.Context) (map[string][]utils.DebtTenantEmailInfo, map[string]utils.LandlordInfo, error) {
	tenantsByLandlord := make(map[string][]utils.DebtTenantEmailInfo)
	landlordInfoMap := make(map[string]utils.LandlordInfo)

	// 查询所有有欠款的活跃租约
	golog.Info("Querying active leases with debt")

	leases, err := entities.GetActiveLeasesWithDebt(ctx)
	if err != nil {
		golog.Error("Failed to query leases", "error", err)
		return nil, nil, fmt.Errorf("failed to get active leases with debt: %v", err)
	}

	golog.Info("Found leases with debt", "count", len(leases))

	// 遍历租约
	for _, lease := range leases {
		// 获取房东信息（如果还没有获取过）
		if _, exists := landlordInfoMap[lease.UserID]; !exists {
			landlordInfo, err := getLandlordInfoForDebtReminder(ctx, lease.UserID, lease.PropertyID)
			if err != nil {
				golog.Error("Failed to get landlord info for debt reminder",
					"landlordId", lease.UserID,
					"propertyId", lease.PropertyID,
					"error", err)
				continue
			}
			landlordInfoMap[lease.UserID] = landlordInfo
		}

		// 获取属性信息用于地址
		property, err := entities.GetPropertyByID(ctx, lease.PropertyID, lease.UserID)
		if err != nil {
			golog.Error("Failed to get property for debt reminder",
				"propertyId", lease.PropertyID,
				"error", err)
			continue
		}

		// 构建属性地址
		addr := property.Address
		propertyAddress := ""
		if addr.Street != "" {
			propertyAddress += addr.Street
		}
		if addr.City != "" {
			if propertyAddress != "" {
				propertyAddress += ", "
			}
			propertyAddress += addr.City
		}
		if addr.Prov != "" {
			if propertyAddress != "" {
				propertyAddress += ", "
			}
			propertyAddress += addr.Prov
		}
		if addr.ZipCode != "" {
			if propertyAddress != "" {
				propertyAddress += " "
			}
			propertyAddress += addr.ZipCode
		}

		// 计算到期日期
		dueDate := calculateDueDate(lease.RentDueDay)

		// 获取当前租户信息，直接按房东分组
		for _, tenant := range lease.CurrentTenants {
			if tenant.Email != "" && tenant.FirstName != "" {
				// 格式化租金
				rentAmount := fmt.Sprintf("%.2f", lease.RentAmount)

				tenantInfo := utils.DebtTenantEmailInfo{
					Email:           tenant.Email,
					FirstName:       tenant.FirstName,
					RentAmount:      rentAmount,
					DueDate:         dueDate,
					PropertyAddress: propertyAddress,
				}

				// 直接按房东ID分组
				tenantsByLandlord[lease.UserID] = append(tenantsByLandlord[lease.UserID], tenantInfo)
			}
		}
	}

	return tenantsByLandlord, landlordInfoMap, nil
}

// getLandlordInfoForDebtReminder 获取房东信息用于债务提醒
func getLandlordInfoForDebtReminder(ctx context.Context, landlordID, propertyID string) (utils.LandlordInfo, error) {
	// 获取房东用户信息
	user, err := entities.GetUserByID(ctx, landlordID)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get landlord user: %v", err)
	}

	// 获取属性信息用于地址
	property, err := entities.GetPropertyByID(ctx, propertyID, landlordID)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get property: %v", err)
	}

	// 构建属性地址
	addr := property.Address
	propertyAddress := ""
	if addr.Street != "" {
		propertyAddress += addr.Street
	}
	if addr.City != "" {
		if propertyAddress != "" {
			propertyAddress += ", "
		}
		propertyAddress += addr.City
	}
	if addr.Prov != "" {
		if propertyAddress != "" {
			propertyAddress += ", "
		}
		propertyAddress += addr.Prov
	}
	if addr.ZipCode != "" {
		if propertyAddress != "" {
			propertyAddress += " "
		}
		propertyAddress += addr.ZipCode
	}

	// 解析用户姓名
	nameParts := strings.Fields(user.Username)
	firstName := ""
	lastName := ""
	if len(nameParts) > 0 {
		firstName = nameParts[0]
		if len(nameParts) > 1 {
			lastName = strings.Join(nameParts[1:], " ")
		}
	}

	return utils.LandlordInfo{
		FirstName:       firstName,
		LastName:        lastName,
		PropertyAddress: propertyAddress,
	}, nil
}

// calculateDueDate 计算到期日期字符串
func calculateDueDate(rentDueDay int) string {
	now := time.Now()

	// 构建当月的到期日期
	dueDate := time.Date(now.Year(), now.Month(), rentDueDay, 0, 0, 0, 0, now.Location())

	// 如果到期日已过，显示下个月的到期日
	if dueDate.Before(now) {
		dueDate = dueDate.AddDate(0, 1, 0)
	}

	return dueDate.Format("January 2, 2006")
}
