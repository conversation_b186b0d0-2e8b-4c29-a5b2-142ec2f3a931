package main

import (
	"context"
	"fmt"
	"os"
	"rent_report/entities"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// Set default config file if none specified
	if os.Getenv("RMBASE_FILE_CFG") == "" {
		os.Setenv("RMBASE_FILE_CFG", "local.ini")
	}

	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		fmt.Printf("Failed to initialize logging: %v\n", err)
		os.Exit(1)
	}

	// Initialize MongoDB
	err := gomongo.InitMongoDB()
	if err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		os.Exit(1)
	}

	// Run rent report email batch processing
	golog.Info("Starting rent report email batch CLI execution...")
	fmt.Println("Starting rent report email batch processing...")

	if err := runRentReportEmailBatch(); err != nil {
		golog.Error("Rent report email batch CLI failed", "error", err)
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	golog.Info("Rent report email batch CLI completed successfully")
	fmt.Println("Rent report email batch processing completed successfully")
}

// runRentReportEmailBatch 运行租金报告邮件批处理
func runRentReportEmailBatch() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 获取配置参数，使用默认值
	processWindowMinutes := 120 // 默认处理前120分钟（2小时）的记录
	if val := goconfig.Config("rentReportEmailBatch.processWindowMinutes"); val != nil {
		if windowInt, ok := val.(int); ok {
			processWindowMinutes = windowInt
		}
	}

	golog.Info("Processing rent report email batch", "windowMinutes", processWindowMinutes)

	// 获取未处理的记录
	records, err := entities.GetUnprocessedRentReportEmailQueue(ctx, processWindowMinutes)
	if err != nil {
		return fmt.Errorf("failed to get unprocessed records: %v", err)
	}

	if len(records) == 0 {
		golog.Info("No unprocessed rent report email records found")
		return nil
	}

	golog.Info("Found unprocessed rent report email records", "count", len(records))

	// 处理每个记录
	successCount := 0
	for _, record := range records {
		err := processEmailRecord(ctx, &record)
		if err != nil {
			golog.Error("Failed to process email record",
				"error", err,
				"recordId", record.ID,
				"tenantId", record.TntId,
				"tenantEmail", record.TntEmail)
			continue
		}

		// 标记为已处理
		err = entities.MarkRentReportEmailQueueProcessed(ctx, record.ID)
		if err != nil {
			golog.Error("Failed to mark record as processed",
				"error", err,
				"recordId", record.ID)
			continue
		}

		successCount++
	}

	golog.Info("Completed rent report email batch processing",
		"totalRecords", len(records),
		"successCount", successCount,
		"failedCount", len(records)-successCount)

	// 执行清理操作
	cleanupDays := 7 // 默认清理7天前的记录
	if val := goconfig.Config("rentReportEmailBatch.cleanupDays"); val != nil {
		if cleanupInt, ok := val.(int); ok {
			cleanupDays = cleanupInt
		}
	}

	err = entities.CleanupOldRentReportEmailQueue(ctx, cleanupDays)
	if err != nil {
		golog.Error("Failed to cleanup old records", "error", err)
		// 不返回错误，清理失败不影响主要功能
	} else {
		golog.Info("Completed cleanup of old rent report email records", "cleanupDays", cleanupDays)
	}

	return nil
}

// processEmailRecord 处理单个邮件记录
func processEmailRecord(ctx context.Context, record *entities.RentReportEmailQueue) error {
	golog.Info("Processing email record",
		"recordId", record.ID,
		"tenantId", record.TntId,
		"tenantEmail", record.TntEmail,
		"initialStatus", record.InitialStatus,
		"finalStatus", record.FinalStatus)

	// 检查状态变化是否需要发送邮件
	if record.InitialStatus == record.FinalStatus {
		golog.Info("No status change, skipping email",
			"status", record.FinalStatus,
			"tenantId", record.TntId)
		return nil
	}

	// 根据最终状态发送邮件
	switch record.FinalStatus {
	case "active":
		return sendRentReportingEnabledEmail(ctx, record)
	case "inactive":
		return sendRentReportingPausedEmail(ctx, record)
	default:
		golog.Warn("Unknown final status, skipping email",
			"finalStatus", record.FinalStatus,
			"tenantId", record.TntId)
		return nil
	}
}

// sendRentReportingEnabledEmail 发送租金报告启用邮件
func sendRentReportingEnabledEmail(ctx context.Context, record *entities.RentReportEmailQueue) error {
	// 获取房东和属性信息
	landlordInfo, err := getLandlordInfo(ctx, record.LandlordId, record.PropId)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 准备租户邮件信息
	tenants := []utils.TenantEmailInfo{
		{
			Email:     record.TntEmail,
			FirstName: record.TntFirstName,
		},
	}

	// 发送启用邮件
	utils.SendRentReportingNotificationEmailsAsync(tenants, landlordInfo)

	golog.Info("Sent rent reporting enabled email",
		"tenantEmail", record.TntEmail,
		"tenantId", record.TntId,
		"leaseId", record.LeaseId)

	return nil
}

// sendRentReportingPausedEmail 发送租金报告暂停邮件
func sendRentReportingPausedEmail(ctx context.Context, record *entities.RentReportEmailQueue) error {
	// 获取房东和属性信息
	landlordInfo, err := getLandlordInfo(ctx, record.LandlordId, record.PropId)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 准备租户邮件信息
	tenants := []utils.TenantEmailInfo{
		{
			Email:     record.TntEmail,
			FirstName: record.TntFirstName,
		},
	}

	// 发送暂停邮件
	utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)

	golog.Info("Sent rent reporting paused email",
		"tenantEmail", record.TntEmail,
		"tenantId", record.TntId,
		"leaseId", record.LeaseId)

	return nil
}

// getLandlordInfo 获取房东信息
func getLandlordInfo(ctx context.Context, landlordId, propId string) (utils.LandlordInfo, error) {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get landlord: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetProperty(ctx, propId, landlordId)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", propId)
		// 不返回错误，使用默认值
	}

	// 构建地址字符串
	propertyAddress := "your property"
	if property != nil {
		propertyAddress = property.Name
		if property.Address.Street != "" {
			propertyAddress = fmt.Sprintf("%s, %s", property.Name, property.Address.Street)
			if property.Address.City != "" {
				propertyAddress = fmt.Sprintf("%s, %s", propertyAddress, property.Address.City)
			}
		}
	}

	return utils.LandlordInfo{
		FirstName:       user.Username,
		LastName:        "",
		PropertyAddress: propertyAddress,
	}, nil
}
